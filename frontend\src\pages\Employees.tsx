import React, { useEffect, useState } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import StatusBadge from '@/components/common/StatusBadge';
import Pagination from '@/components/common/Pagination';
import { apiService } from '@/services/api';
import { Employee, EmployeeFilters } from '@/types/api';
import { formatDate, formatCurrency, getErrorMessage, debounce } from '@/lib/utils';
import { usePermissions } from '@/contexts/AuthContext';

const Employees: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEmployees, setTotalEmployees] = useState(0);
  const [filters, setFilters] = useState<EmployeeFilters>({
    page: 1,
    limit: 20,
  });

  const permissions = usePermissions();

  const fetchEmployees = async (newFilters?: EmployeeFilters) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const filtersToUse = newFilters || filters;
      const response = await apiService.getEmployees(filtersToUse);
      
      setEmployees(response.data);
      setTotalEmployees(response.total);
      setTotalPages(response.pages);
      setCurrentPage(response.page);
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const debouncedSearch = debounce((searchTerm: string) => {
    const newFilters = { ...filters, search: searchTerm, page: 1 };
    setFilters(newFilters);
    fetchEmployees(newFilters);
  }, 300);

  const handleSearch = (value: string) => {
    debouncedSearch(value);
  };

  const handleStatusFilter = (status: string) => {
    const newFilters = { 
      ...filters, 
      status: status === 'all' ? undefined : status as 'active' | 'inactive',
      page: 1 
    };
    setFilters(newFilters);
    fetchEmployees(newFilters);
  };

  const handleDepartmentFilter = (department: string) => {
    const newFilters = { 
      ...filters, 
      department: department === 'all' ? undefined : department,
      page: 1 
    };
    setFilters(newFilters);
    fetchEmployees(newFilters);
  };

  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    fetchEmployees(newFilters);
  };

  const handleViewEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsDialogOpen(true);
  };

  const handleDeleteEmployee = async (employee: Employee) => {
    if (!permissions.canManageEmployees()) return;
    
    if (window.confirm(`Are you sure you want to delete ${employee.full_name}?`)) {
      try {
        await apiService.deleteEmployee(employee.id);
        fetchEmployees();
      } catch (err) {
        setError(getErrorMessage(err));
      }
    }
  };

  // Get unique departments for filter
  const departments = Array.from(new Set(employees.map(emp => emp.department).filter(Boolean)));

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Employees</h1>
          <p className="text-muted-foreground">
            Manage employee profiles and information
          </p>
        </div>
        {permissions.canManageEmployees() && (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Employee
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search employees..."
                  className="pl-8"
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
            </div>
            <Select onValueChange={handleStatusFilter} defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select onValueChange={handleDepartmentFilter} defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {departments.map((dept) => (
                  <SelectItem key={dept} value={dept!}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Employees Table */}
      <Card>
        <CardHeader>
          <CardTitle>Employees ({totalEmployees})</CardTitle>
          <CardDescription>
            A list of all employees in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && <ErrorMessage message={error} className="mb-4" />}
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>RFID Tag</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Position</TableHead>
                    <TableHead>Hourly Rate</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employees.map((employee) => (
                    <TableRow key={employee.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{employee.full_name}</div>
                          <div className="text-sm text-muted-foreground">{employee.email}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{employee.rfid_tag}</TableCell>
                      <TableCell>{employee.department || '-'}</TableCell>
                      <TableCell>{employee.position || '-'}</TableCell>
                      <TableCell>{formatCurrency(employee.hourly_rate)}</TableCell>
                      <TableCell>
                        <StatusBadge status={employee.status} />
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleViewEmployee(employee)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {permissions.canManageEmployees() && (
                            <>
                              <Button variant="ghost" size="icon">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteEmployee(employee)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {employees.length === 0 && !isLoading && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No employees found</p>
                </div>
              )}

              {totalPages > 1 && (
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Employee Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Employee Details</DialogTitle>
            <DialogDescription>
              Detailed information about the selected employee
            </DialogDescription>
          </DialogHeader>
          {selectedEmployee && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Full Name</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.full_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">RFID Tag</label>
                  <p className="text-sm text-muted-foreground font-mono">{selectedEmployee.rfid_tag}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.email || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Phone</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.phone || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Department</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.department || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Position</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.position || '-'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Hourly Rate</label>
                  <p className="text-sm text-muted-foreground">{formatCurrency(selectedEmployee.hourly_rate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Employment Start Date</label>
                  <p className="text-sm text-muted-foreground">{formatDate(selectedEmployee.employment_start_date)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Leave Balance</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.leave_balance} days</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Sick Leave Balance</label>
                  <p className="text-sm text-muted-foreground">{selectedEmployee.sick_leave_balance} days</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <StatusBadge status={selectedEmployee.status} />
                </div>
                <div>
                  <label className="text-sm font-medium">Created</label>
                  <p className="text-sm text-muted-foreground">{formatDate(selectedEmployee.created_at)}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Employees;
