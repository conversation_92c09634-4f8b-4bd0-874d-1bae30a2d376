/*
 * Time and Attendance System - ESP32 Firmware
 *
 * This firmware handles RFID scanning, TFT display, and communication
 * with the backend server for employee time tracking.
 *
 * Hardware Requirements:
 * - ESP32 Development Board
 * - MFRC522 RFID Reader
 * - 1.8" TFT SPI Display (128x160, v1.1)
 * - WiFi connectivity
 *
 * Author: Time Attendance System
 * Version: 1.0.0
 */

#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <SPI.h>
#include <MFRC522.h>
#include <TFT_eSPI.h>
#include <NTPClient.h>
#include <WiFiUdp.h>
#include <SPIFFS.h>
#include <Preferences.h>

// Pin definitions for MFRC522
#define RST_PIN         22
#define SS_PIN          21

// Pin definitions for TFT (configured in TFT_eSPI library)
// MOSI = 23, MISO = 19, SCK = 18, CS = 15, DC = 2, RST = 4

// WiFi credentials (change these)
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// Server configuration (change these)
const char* serverURL = "http://your-server.com:5000";
const char* deviceAPIKey = "your-device-api-key";
const char* deviceID = "ESP32_001";

// NTP configuration
const char* ntpServer = "pool.ntp.org";
const long gmtOffset_sec = 0;  // UTC offset in seconds
const int daylightOffset_sec = 0;

// Auto checkout time (24-hour format)
const int autoCheckoutHour = 19;
const int autoCheckoutMinute = 30;

// Object instantiations
MFRC522 mfrc522(SS_PIN, RST_PIN);
TFT_eSPI tft = TFT_eSPI();
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, ntpServer, gmtOffset_sec, 60000);
Preferences preferences;

// Global variables
bool wifiConnected = false;
bool serverConnected = false;
unsigned long lastSyncAttempt = 0;
unsigned long lastDisplayUpdate = 0;
unsigned long lastAutoCheckoutCheck = 0;
const unsigned long syncInterval = 300000; // 5 minutes
const unsigned long displayUpdateInterval = 1000; // 1 second
const unsigned long autoCheckoutCheckInterval = 60000; // 1 minute

// Employee status tracking
struct EmployeeStatus {
  String rfidTag;
  String name;
  bool isClockIn;
  unsigned long lastScanTime;
};

EmployeeStatus lastEmployee = {"", "", false, 0};

// Function prototypes
void setupWiFi();
void setupDisplay();
void setupRFID();
void setupNTP();
void setupSPIFFS();
void displayStatus();
void displayEmployeeInfo(String name, String status);
void handleRFIDScan();
void processAttendance(String rfidTag);
void syncWithServer();
void checkAutoCheckout();
void storeAttendanceRecord(String rfidTag, String type, String timestamp);
String getCurrentTimestamp();
String getEmployeeName(String rfidTag);
bool sendAttendanceToServer(String rfidTag, String type, String timestamp);
void displayError(String message);
void displaySuccess(String message);

void setup() {
  Serial.begin(115200);
  Serial.println("Starting Time and Attendance System...");

  // Initialize preferences
  preferences.begin("attendance", false);

  // Initialize SPIFFS
  setupSPIFFS();

  // Initialize SPI
  SPI.begin();

  // Initialize RFID
  setupRFID();

  // Initialize display
  setupDisplay();

  // Initialize WiFi
  setupWiFi();

  // Initialize NTP
  setupNTP();

  Serial.println("System initialization complete!");
  displayStatus();
}

void loop() {
  // Update time client
  timeClient.update();

  // Check for RFID scan
  handleRFIDScan();

  // Update display periodically
  if (millis() - lastDisplayUpdate > displayUpdateInterval) {
    displayStatus();
    lastDisplayUpdate = millis();
  }

  // Sync with server periodically
  if (millis() - lastSyncAttempt > syncInterval) {
    syncWithServer();
    lastSyncAttempt = millis();
  }

  // Check for auto checkout
  if (millis() - lastAutoCheckoutCheck > autoCheckoutCheckInterval) {
    checkAutoCheckout();
    lastAutoCheckoutCheck = millis();
  }

  // Small delay to prevent watchdog issues
  delay(100);
}

void setupWiFi() {
  Serial.print("Connecting to WiFi");
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("Connecting to WiFi...");

  WiFi.begin(ssid, password);

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    tft.print(".");
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    Serial.println();
    Serial.println("WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());

    tft.println();
    tft.println("WiFi Connected!");
    tft.print("IP: ");
    tft.println(WiFi.localIP());
  } else {
    wifiConnected = false;
    Serial.println();
    Serial.println("WiFi connection failed!");
    tft.println();
    tft.setTextColor(TFT_RED);
    tft.println("WiFi Failed!");
  }

  delay(2000);
}

void setupDisplay() {
  tft.init();
  tft.setRotation(1); // Landscape orientation
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);
  tft.println("Time & Attendance");
  tft.println("System Starting...");
}

void setupRFID() {
  mfrc522.PCD_Init();
  Serial.println("RFID reader initialized");

  // Test RFID reader
  byte version = mfrc522.PCD_ReadRegister(mfrc522.VersionReg);
  if (version == 0x00 || version == 0xFF) {
    Serial.println("WARNING: RFID reader not found!");
    tft.setTextColor(TFT_RED);
    tft.println("RFID Error!");
  } else {
    Serial.print("RFID reader version: 0x");
    Serial.println(version, HEX);
    tft.setTextColor(TFT_GREEN);
    tft.println("RFID Ready");
  }
}

void setupNTP() {
  if (wifiConnected) {
    timeClient.begin();
    timeClient.update();
    Serial.println("NTP client initialized");
    tft.setTextColor(TFT_GREEN);
    tft.println("Time Synced");
  } else {
    Serial.println("Cannot initialize NTP without WiFi");
    tft.setTextColor(TFT_RED);
    tft.println("Time Sync Failed");
  }
}

void setupSPIFFS() {
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS initialization failed!");
    return;
  }
  Serial.println("SPIFFS initialized");
}

void displayStatus() {
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  tft.setCursor(0, 0);

  // Title
  tft.setTextColor(TFT_CYAN);
  tft.setTextSize(2);
  tft.println("Time & Attendance");
  tft.println();

  // Date and time
  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  if (wifiConnected && timeClient.isTimeSet()) {
    String formattedTime = timeClient.getFormattedTime();
    String formattedDate = timeClient.getFormattedDate();
    tft.println("Date: " + formattedDate.substring(0, 10));
    tft.println("Time: " + formattedTime);
  } else {
    tft.println("Date: --/--/----");
    tft.println("Time: --:--:--");
  }
  tft.println();

  // Status indicators
  tft.print("WiFi: ");
  if (wifiConnected) {
    tft.setTextColor(TFT_GREEN);
    tft.println("Connected");
  } else {
    tft.setTextColor(TFT_RED);
    tft.println("Disconnected");
  }

  tft.setTextColor(TFT_WHITE);
  tft.print("Server: ");
  if (serverConnected) {
    tft.setTextColor(TFT_GREEN);
    tft.println("Online");
  } else {
    tft.setTextColor(TFT_YELLOW);
    tft.println("Offline");
  }

  tft.println();
  tft.setTextColor(TFT_CYAN);
  tft.println("Scan RFID Card");

  // Last employee info
  if (lastEmployee.rfidTag != "" && (millis() - lastEmployee.lastScanTime) < 10000) {
    tft.println();
    tft.setTextColor(TFT_YELLOW);
    tft.println("Last Scan:");
    tft.setTextColor(TFT_WHITE);
    tft.println(lastEmployee.name);
    tft.setTextColor(lastEmployee.isClockIn ? TFT_GREEN : TFT_RED);
    tft.println(lastEmployee.isClockIn ? "CLOCKED IN" : "CLOCKED OUT");
  }
}

void handleRFIDScan() {
  // Check for new RFID card
  if (!mfrc522.PICC_IsNewCardPresent() || !mfrc522.PICC_ReadCardSerial()) {
    return;
  }

  // Read RFID tag
  String rfidTag = "";
  for (byte i = 0; i < mfrc522.uid.size; i++) {
    rfidTag += String(mfrc522.uid.uidByte[i] < 0x10 ? "0" : "");
    rfidTag += String(mfrc522.uid.uidByte[i], HEX);
  }
  rfidTag.toUpperCase();

  Serial.println("RFID Tag scanned: " + rfidTag);

  // Process attendance
  processAttendance(rfidTag);

  // Halt PICC
  mfrc522.PICC_HaltA();
  mfrc522.PCD_StopCrypto1();

  // Prevent multiple scans
  delay(2000);
}

void processAttendance(String rfidTag) {
  String timestamp = getCurrentTimestamp();

  // Determine if this is clock in or clock out
  bool isClockIn = true;
  String lastType = preferences.getString("last_type_" + rfidTag, "clock_out");
  if (lastType == "clock_in") {
    isClockIn = false;
  }

  String type = isClockIn ? "clock_in" : "clock_out";
  String employeeName = getEmployeeName(rfidTag);

  // Store locally first
  storeAttendanceRecord(rfidTag, type, timestamp);

  // Update last employee info
  lastEmployee.rfidTag = rfidTag;
  lastEmployee.name = employeeName;
  lastEmployee.isClockIn = isClockIn;
  lastEmployee.lastScanTime = millis();

  // Store last type for this employee
  preferences.putString("last_type_" + rfidTag, type);

  // Try to send to server
  bool sent = false;
  if (wifiConnected) {
    sent = sendAttendanceToServer(rfidTag, type, timestamp);
  }

  // Display result
  if (sent) {
    displaySuccess(employeeName + "\n" + (isClockIn ? "CLOCKED IN" : "CLOCKED OUT"));
    serverConnected = true;
  } else {
    displaySuccess(employeeName + "\n" + (isClockIn ? "CLOCKED IN" : "CLOCKED OUT") + "\n(Stored Locally)");
    serverConnected = false;
  }

  Serial.println("Attendance recorded: " + employeeName + " - " + type);
}

void syncWithServer() {
  if (!wifiConnected) {
    return;
  }

  Serial.println("Syncing with server...");

  // Read pending records from SPIFFS
  File file = SPIFFS.open("/pending_records.txt", "r");
  if (!file) {
    Serial.println("No pending records to sync");
    return;
  }

  String pendingRecords = file.readString();
  file.close();

  if (pendingRecords.length() == 0) {
    Serial.println("No pending records to sync");
    return;
  }

  // Parse and send records
  HTTPClient http;
  http.begin(String(serverURL) + "/api/attendance/batch");
  http.addHeader("Content-Type", "application/json");
  http.addHeader("X-API-Key", deviceAPIKey);

  // Create JSON payload
  DynamicJsonDocument doc(4096);
  JsonArray records = doc.createNestedArray("records");

  // Parse pending records (simple CSV format)
  int startIndex = 0;
  int endIndex = pendingRecords.indexOf('\n');

  while (endIndex != -1) {
    String record = pendingRecords.substring(startIndex, endIndex);

    // Parse CSV: timestamp,rfid_tag,type,synced
    int comma1 = record.indexOf(',');
    int comma2 = record.indexOf(',', comma1 + 1);
    int comma3 = record.indexOf(',', comma2 + 1);

    if (comma1 != -1 && comma2 != -1 && comma3 != -1) {
      String timestamp = record.substring(0, comma1);
      String rfidTag = record.substring(comma1 + 1, comma2);
      String type = record.substring(comma2 + 1, comma3);
      String synced = record.substring(comma3 + 1);

      if (synced == "0") { // Not synced yet
        JsonObject recordObj = records.createNestedObject();
        recordObj["employee_rfid"] = rfidTag;
        recordObj["timestamp"] = timestamp;
        recordObj["type"] = type;
        recordObj["device_id"] = deviceID;
        recordObj["status"] = "regular";
      }
    }

    startIndex = endIndex + 1;
    endIndex = pendingRecords.indexOf('\n', startIndex);
  }

  if (records.size() == 0) {
    Serial.println("No unsynced records found");
    return;
  }

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if (httpResponseCode == 200) {
    String response = http.getString();
    Serial.println("Sync successful: " + response);

    // Mark records as synced
    markRecordsAsSynced();
    serverConnected = true;
  } else {
    Serial.println("Sync failed. HTTP code: " + String(httpResponseCode));
    serverConnected = false;
  }

  http.end();
}

void checkAutoCheckout() {
  if (!wifiConnected || !timeClient.isTimeSet()) {
    return;
  }

  int currentHour = timeClient.getHours();
  int currentMinute = timeClient.getMinutes();

  // Check if it's auto checkout time
  if (currentHour == autoCheckoutHour && currentMinute == autoCheckoutMinute) {
    Serial.println("Performing auto checkout...");

    // Get all employees who are currently clocked in
    // This is a simplified version - in a real implementation,
    // you might want to track this more systematically

    // For now, we'll just log the auto checkout event
    String timestamp = getCurrentTimestamp();

    // You could implement logic here to auto-checkout employees
    // who haven't clocked out by checking the last status of each RFID tag

    Serial.println("Auto checkout check completed at " + timestamp);
  }
}

void storeAttendanceRecord(String rfidTag, String type, String timestamp) {
  // Store in SPIFFS as CSV format
  File file = SPIFFS.open("/pending_records.txt", "a");
  if (file) {
    file.println(timestamp + "," + rfidTag + "," + type + ",0"); // 0 = not synced
    file.close();
    Serial.println("Record stored locally");
  } else {
    Serial.println("Failed to store record locally");
  }
}

void markRecordsAsSynced() {
  // Read all records
  File file = SPIFFS.open("/pending_records.txt", "r");
  if (!file) {
    return;
  }

  String allRecords = file.readString();
  file.close();

  // Mark all as synced (change 0 to 1)
  allRecords.replace(",0\n", ",1\n");

  // Write back
  file = SPIFFS.open("/pending_records.txt", "w");
  if (file) {
    file.print(allRecords);
    file.close();
  }
}

String getCurrentTimestamp() {
  if (wifiConnected && timeClient.isTimeSet()) {
    unsigned long epochTime = timeClient.getEpochTime();

    // Convert to ISO format
    time_t rawtime = epochTime;
    struct tm * timeinfo = gmtime(&rawtime);

    char buffer[25];
    strftime(buffer, sizeof(buffer), "%Y-%m-%dT%H:%M:%SZ", timeinfo);

    return String(buffer);
  } else {
    // Fallback to millis-based timestamp
    return String(millis());
  }
}

String getEmployeeName(String rfidTag) {
  // In a real implementation, you might cache employee names locally
  // For now, return a placeholder based on RFID tag
  if (rfidTag == "ABCD1234") return "John Doe";
  if (rfidTag == "EFGH5678") return "Jane Smith";
  if (rfidTag == "IJKL9012") return "Bob Johnson";
  if (rfidTag == "MNOP3456") return "Alice Brown";

  return "Employee " + rfidTag.substring(0, 4);
}

bool sendAttendanceToServer(String rfidTag, String type, String timestamp) {
  if (!wifiConnected) {
    return false;
  }

  HTTPClient http;
  http.begin(String(serverURL) + "/api/attendance");
  http.addHeader("Content-Type", "application/json");
  http.addHeader("X-API-Key", deviceAPIKey);

  // Create JSON payload
  DynamicJsonDocument doc(1024);
  doc["employee_rfid"] = rfidTag;
  doc["timestamp"] = timestamp;
  doc["type"] = type;
  doc["device_id"] = deviceID;
  doc["status"] = "regular";

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  bool success = (httpResponseCode == 201);

  if (success) {
    Serial.println("Attendance sent to server successfully");
  } else {
    Serial.println("Failed to send attendance to server. HTTP code: " + String(httpResponseCode));
    if (httpResponseCode > 0) {
      Serial.println("Response: " + http.getString());
    }
  }

  http.end();
  return success;
}

void displayError(String message) {
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_RED);
  tft.setTextSize(2);
  tft.setCursor(10, 50);
  tft.println("ERROR");

  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  tft.setCursor(10, 80);
  tft.println(message);

  delay(3000);
}

void displaySuccess(String message) {
  tft.fillScreen(TFT_BLACK);
  tft.setTextColor(TFT_GREEN);
  tft.setTextSize(2);
  tft.setCursor(10, 30);
  tft.println("SUCCESS");

  tft.setTextColor(TFT_WHITE);
  tft.setTextSize(1);
  tft.setCursor(10, 60);
  tft.println(message);

  delay(3000);
}
