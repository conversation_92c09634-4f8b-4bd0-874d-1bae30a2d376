# Time and Attendance System

A comprehensive IoT-based time and attendance tracking system using ESP32, RFID technology, and a web-based management dashboard.

## 🚀 Features

### Hardware (ESP32 Device)
- **RFID Card Scanning**: Employees clock in/out using RFID cards
- **TFT Display**: Real-time status display with date, time, and employee information
- **WiFi Connectivity**: Automatic synchronization with backend server
- **Offline Operation**: Local storage when network is unavailable
- **Auto Checkout**: Automatic checkout at configurable time (default 7:30 PM)
- **Visual Feedback**: Color-coded status indicators and employee feedback

### Backend API
- **RESTful API**: Complete REST API for all operations
- **JWT Authentication**: Secure user authentication and authorization
- **Role-based Access**: Admin, Manager, and Viewer roles
- **Device Management**: Register and manage multiple ESP32 devices
- **Data Validation**: Comprehensive input validation and error handling
- **Rate Limiting**: Protection against API abuse
- **Audit Logging**: Complete audit trail of all system actions

### Data Management
- **Employee Management**: Complete employee profiles with RFID tag assignment
- **Attendance Tracking**: Detailed attendance records with timestamps
- **Leave Management**: Leave request submission and approval workflow
- **Wage Calculation**: Automatic wage calculation based on hours worked
- **Report Generation**: PDF and CSV reports for attendance, leave, and payroll

### Security
- **Encrypted Communication**: HTTPS/TLS for all data transmission
- **API Key Authentication**: Secure device authentication
- **Password Hashing**: Bcrypt password hashing
- **Input Sanitization**: Protection against SQL injection and XSS
- **Session Management**: Secure JWT token handling

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ESP32 Device  │    │  Backend API    │    │   Web Dashboard │
│                 │    │                 │    │                 │
│ • RFID Scanner  │◄──►│ • Flask API     │◄──►│ • React Frontend│
│ • TFT Display   │    │ • MySQL DB      │    │ • Admin Panel   │
│ • WiFi Module   │    │ • JWT Auth      │    │ • Reports       │
│ • Local Storage │    │ • Rate Limiting │    │ • Real-time UI  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Requirements

### Hardware
- ESP32 Development Board
- MFRC522 RFID Reader Module
- 1.8" TFT SPI Display (128x160, ST7735)
- RFID Cards/Tags (13.56MHz)
- Power supply (5V/2A)
- Breadboard and jumper wires

### Software
- Python 3.8+
- MySQL/MariaDB 8.0+
- Arduino IDE 1.8.19+
- Node.js 16+ (for frontend development)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/time-attendance-system.git
cd time-attendance-system
```

### 2. Backend Setup
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your database credentials

# Initialize database
python backend/database/init_db.py

# Start the server
python run_backend.py
```

### 3. ESP32 Setup
1. Install Arduino IDE and required libraries
2. Configure TFT_eSPI library for your display
3. Update WiFi and server settings in the firmware
4. Upload firmware to ESP32
5. Register device in the backend system

### 4. Access the System
- **Backend API**: http://localhost:5000
- **Default Login**: admin / admin123
- **API Documentation**: http://localhost:5000/docs (if implemented)

For detailed setup instructions, see [SETUP_GUIDE.md](SETUP_GUIDE.md)

## 📁 Project Structure

```
time_attendance_system/
├── backend/                    # Flask backend application
│   ├── app.py                 # Main application factory
│   ├── models.py              # Database models
│   ├── routes/                # API route handlers
│   │   ├── auth.py           # Authentication routes
│   │   ├── employees.py      # Employee management
│   │   ├── attendance.py     # Attendance tracking
│   │   ├── leave.py          # Leave management
│   │   ├── devices.py        # Device management
│   │   ├── reports.py        # Report generation
│   │   ├── users.py          # User management
│   │   └── settings.py       # System settings
│   ├── utils/                 # Utility functions
│   │   ├── validators.py     # Input validation
│   │   ├── logging.py        # Logging utilities
│   │   └── auth.py           # Authentication utilities
│   └── database/              # Database related files
│       ├── schema.sql        # Database schema
│       └── init_db.py        # Database initialization
├── esp32_firmware/            # ESP32 firmware
│   ├── time_attendance_system/
│   │   ├── time_attendance_system.ino  # Main firmware
│   │   └── config.h          # Configuration file
│   └── README.md             # Firmware documentation
├── frontend/                  # React frontend (future)
├── docs/                      # Documentation
│   ├── backend_architecture.md
│   ├── esp32_firmware_design.md
│   ├── hardware_connection_diagram.md
│   ├── implementation_guide.md
│   ├── secure_data_sync.md
│   ├── system_requirements.md
│   ├── system_validation.md
│   └── web_dashboard_design.md
├── requirements.txt           # Python dependencies
├── .env.example              # Environment configuration template
├── run_backend.py            # Backend run script
├── SETUP_GUIDE.md            # Detailed setup instructions
└── README.md                 # This file
```

## 🔧 Configuration

### Backend Configuration (.env)
```env
# Database
DATABASE_URL=mysql+pymysql://user:password@localhost/time_attendance

# Security
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret-key

# Email (optional)
MAIL_SERVER=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### ESP32 Configuration
```cpp
// WiFi Settings
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// Server Settings
const char* serverURL = "http://your-server.com:5000";
const char* deviceAPIKey = "your-device-api-key";
```

## 📊 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh token

### Employees
- `GET /api/employees` - List employees
- `POST /api/employees` - Create employee
- `PUT /api/employees/{id}` - Update employee
- `DELETE /api/employees/{id}` - Delete employee

### Attendance
- `GET /api/attendance` - List attendance records
- `POST /api/attendance` - Create attendance record (ESP32)
- `POST /api/attendance/batch` - Batch sync (ESP32)

### Reports
- `GET /api/reports/attendance` - Attendance report
- `GET /api/reports/leave` - Leave report
- `GET /api/reports/payroll` - Payroll report

For complete API documentation, see [backend_architecture.md](backend_architecture.md)

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **API Key Protection**: Device-specific API keys
- **Password Hashing**: Bcrypt password encryption
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive data validation
- **HTTPS Support**: Encrypted data transmission
- **Audit Logging**: Complete action audit trail

## 📈 Monitoring and Maintenance

### Health Checks
- `GET /health` - System health status
- Device connectivity monitoring
- Database connection monitoring

### Logging
- Application logs in `logs/app.log`
- System audit logs in database
- ESP32 debug output via Serial Monitor

### Backups
- Automated database backups
- Configuration file backups
- Log file rotation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- [Setup Guide](SETUP_GUIDE.md) - Complete installation instructions
- [System Requirements](system_requirements.md) - Detailed requirements analysis
- [Hardware Guide](hardware_connection_diagram.md) - Wiring and assembly
- [Firmware Guide](esp32_firmware/README.md) - ESP32 firmware documentation

### Troubleshooting
- Check the [SETUP_GUIDE.md](SETUP_GUIDE.md) troubleshooting section
- Review log files for error messages
- Verify hardware connections
- Test network connectivity

### Getting Help
- Open an issue on GitHub
- Check existing documentation
- Review the troubleshooting guides

## 🎯 Roadmap

### Current Version (v1.0)
- ✅ Basic RFID attendance tracking
- ✅ Web-based management interface
- ✅ Report generation
- ✅ Multi-device support

### Future Enhancements (v2.0)
- 🔄 Mobile application
- 🔄 Biometric authentication
- 🔄 Advanced analytics
- 🔄 Integration with payroll systems
- 🔄 Real-time notifications
- 🔄 Multi-tenant support

---

**Built with ❤️ for efficient workforce management**
