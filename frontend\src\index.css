@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .sidebar-nav {
    @apply flex flex-col space-y-1;
  }
  
  .sidebar-nav-item {
    @apply flex items-center rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors;
  }
  
  .sidebar-nav-item.active {
    @apply bg-accent text-accent-foreground;
  }
  
  .data-table {
    @apply w-full border-collapse border border-border;
  }
  
  .data-table th {
    @apply border border-border bg-muted px-4 py-2 text-left font-medium;
  }
  
  .data-table td {
    @apply border border-border px-4 py-2;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .status-badge.active {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge.inactive {
    @apply bg-red-100 text-red-800;
  }
  
  .status-badge.pending {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-badge.approved {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge.rejected {
    @apply bg-red-100 text-red-800;
  }
  
  .status-badge.online {
    @apply bg-green-100 text-green-800;
  }
  
  .status-badge.offline {
    @apply bg-gray-100 text-gray-800;
  }
  
  .form-error {
    @apply text-sm text-destructive mt-1;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary;
  }
  
  .page-header {
    @apply flex items-center justify-between pb-4 border-b border-border mb-6;
  }
  
  .page-title {
    @apply text-2xl font-bold tracking-tight;
  }
  
  .card-stats {
    @apply bg-card text-card-foreground rounded-lg border p-6 shadow-sm;
  }
  
  .card-stats-title {
    @apply text-sm font-medium text-muted-foreground;
  }
  
  .card-stats-value {
    @apply text-2xl font-bold;
  }
  
  .card-stats-change {
    @apply text-xs text-muted-foreground;
  }
}
