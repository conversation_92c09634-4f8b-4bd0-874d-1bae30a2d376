#!/usr/bin/env python3
"""
Test script for the Time and Attendance System Backend
This script tests the basic functionality of the backend API
"""

import requests
import json
import sys
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

class BackendTester:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.device_api_key = None
    
    def test_health_check(self):
        """Test if the server is running"""
        print("🔍 Testing health check...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to server. Is it running?")
            return False
    
    def test_login(self):
        """Test user authentication"""
        print("🔍 Testing login...")
        try:
            response = self.session.post(
                f"{self.base_url}/api/auth/login",
                json={
                    "username": ADMIN_USERNAME,
                    "password": ADMIN_PASSWORD
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('access_token')
                self.session.headers.update({
                    'Authorization': f'Bearer {self.auth_token}'
                })
                print("✅ Login successful")
                return True
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Login error: {str(e)}")
            return False
    
    def test_employees(self):
        """Test employee management"""
        print("🔍 Testing employee management...")
        
        # Test getting employees
        response = self.session.get(f"{self.base_url}/api/employees")
        if response.status_code != 200:
            print(f"❌ Failed to get employees: {response.status_code}")
            return False
        
        employees = response.json()
        print(f"✅ Retrieved {employees.get('total', 0)} employees")
        
        # Test creating an employee
        test_employee = {
            "rfid_tag": "TEST1234",
            "first_name": "Test",
            "last_name": "Employee",
            "email": "<EMAIL>",
            "hourly_rate": 25.00,
            "employment_start_date": "2024-01-01"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/employees",
            json=test_employee
        )
        
        if response.status_code == 201:
            employee_data = response.json()
            employee_id = employee_data['id']
            print(f"✅ Created test employee with ID: {employee_id}")
            
            # Clean up - delete the test employee
            delete_response = self.session.delete(f"{self.base_url}/api/employees/{employee_id}")
            if delete_response.status_code == 200:
                print("✅ Cleaned up test employee")
            
            return True
        else:
            print(f"❌ Failed to create employee: {response.status_code} - {response.text}")
            return False
    
    def test_devices(self):
        """Test device management"""
        print("🔍 Testing device management...")
        
        # Test getting devices
        response = self.session.get(f"{self.base_url}/api/devices")
        if response.status_code != 200:
            print(f"❌ Failed to get devices: {response.status_code}")
            return False
        
        devices = response.json()
        print(f"✅ Retrieved {devices.get('total', 0)} devices")
        
        # Test creating a device
        test_device = {
            "name": "Test Scanner",
            "location": "Test Location",
            "mac_address": "AA:BB:CC:DD:EE:FF"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/devices",
            json=test_device
        )
        
        if response.status_code == 201:
            device_data = response.json()
            device_id = device_data['id']
            self.device_api_key = device_data['api_key']
            print(f"✅ Created test device with ID: {device_id}")
            print(f"📝 Device API Key: {self.device_api_key[:20]}...")
            
            # Clean up - delete the test device
            delete_response = self.session.delete(f"{self.base_url}/api/devices/{device_id}")
            if delete_response.status_code == 200:
                print("✅ Cleaned up test device")
            
            return True
        else:
            print(f"❌ Failed to create device: {response.status_code} - {response.text}")
            return False
    
    def test_attendance_api(self):
        """Test attendance API (device endpoint)"""
        print("🔍 Testing attendance API...")
        
        if not self.device_api_key:
            print("⚠️ Skipping attendance test - no device API key")
            return True
        
        # Test attendance submission
        attendance_data = {
            "employee_rfid": "ABCD1234",  # Should exist from sample data
            "timestamp": datetime.now().isoformat() + "Z",
            "type": "clock_in",
            "device_id": "TEST_DEVICE"
        }
        
        headers = {
            "X-API-Key": self.device_api_key,
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{self.base_url}/api/attendance",
            json=attendance_data,
            headers=headers
        )
        
        if response.status_code == 201:
            print("✅ Attendance record created successfully")
            return True
        else:
            print(f"❌ Failed to create attendance record: {response.status_code} - {response.text}")
            return False
    
    def test_reports(self):
        """Test report generation"""
        print("🔍 Testing report generation...")
        
        # Test attendance report
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "format": "json"
        }
        
        response = self.session.get(
            f"{self.base_url}/api/reports/attendance",
            params=params
        )
        
        if response.status_code == 200:
            report_data = response.json()
            print(f"✅ Generated attendance report with {len(report_data.get('data', []))} records")
            return True
        else:
            print(f"❌ Failed to generate report: {response.status_code} - {response.text}")
            return False
    
    def test_settings(self):
        """Test settings management"""
        print("🔍 Testing settings management...")
        
        # Test getting settings
        response = self.session.get(f"{self.base_url}/api/settings")
        if response.status_code == 200:
            settings = response.json()
            print(f"✅ Retrieved {len(settings.get('data', []))} settings")
            return True
        else:
            print(f"❌ Failed to get settings: {response.status_code}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Time and Attendance System Backend Tests")
        print("=" * 60)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Authentication", self.test_login),
            ("Employee Management", self.test_employees),
            ("Device Management", self.test_devices),
            ("Attendance API", self.test_attendance_api),
            ("Report Generation", self.test_reports),
            ("Settings Management", self.test_settings),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}")
            print("-" * 40)
            
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} ERROR: {str(e)}")
        
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Backend is working correctly.")
            return True
        else:
            print("⚠️ Some tests failed. Check the output above for details.")
            return False

def main():
    """Main function"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = BASE_URL
    
    print(f"🔗 Testing backend at: {base_url}")
    
    tester = BackendTester(base_url)
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 Next steps:")
        print("1. Configure your ESP32 device with the server URL")
        print("2. Register your ESP32 device in the system")
        print("3. Add your employees to the system")
        print("4. Start using the attendance system!")
        sys.exit(0)
    else:
        print("\n🔧 Troubleshooting:")
        print("1. Make sure the backend server is running")
        print("2. Check the database connection")
        print("3. Verify the .env configuration")
        print("4. Check the server logs for errors")
        sys.exit(1)

if __name__ == "__main__":
    main()
