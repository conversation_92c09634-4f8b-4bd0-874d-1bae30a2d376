"""
Authentication routes for the Time and Attendance System
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import (
    create_access_token, create_refresh_token, jwt_required, 
    get_jwt_identity, get_jwt
)
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

from backend.models import User, SystemLog, db
from backend.utils.validators import validate_login_data
from backend.utils.logging import log_user_action

auth_bp = Blueprint('auth', __name__)

# JWT token blacklist (in production, use Redis)
blacklisted_tokens = set()

@auth_bp.route('/login', methods=['POST'])
def login():
    """
    Authenticate user and return JWT tokens
    """
    try:
        # Validate request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        validation_error = validate_login_data(data)
        if validation_error:
            return jsonify({'error': validation_error}), 400
        
        username = data.get('username')
        password = data.get('password')
        
        # Find user
        user = User.query.filter_by(username=username).first()
        
        if not user or not user.check_password(password):
            # Log failed login attempt
            current_app.logger.warning(
                f'Failed login attempt for username: {username} from IP: {request.remote_addr}'
            )
            return jsonify({'error': 'Invalid credentials'}), 401
        
        # Check if user is active
        if user.status != 'active':
            current_app.logger.warning(
                f'Login attempt for inactive user: {username} from IP: {request.remote_addr}'
            )
            return jsonify({'error': 'Account is inactive'}), 401
        
        # Update last login
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # Create tokens
        access_token = create_access_token(identity=user.id)
        refresh_token = create_refresh_token(identity=user.id)
        
        # Log successful login
        log_user_action(
            user_id=user.id,
            action='login',
            details=f'Successful login from IP: {request.remote_addr}',
            ip_address=request.remote_addr
        )
        
        current_app.logger.info(
            f'Successful login: {username} from IP: {request.remote_addr}'
        )
        
        return jsonify({
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Login error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """
    Logout user and blacklist token
    """
    try:
        # Get current token
        jti = get_jwt()['jti']
        user_id = get_jwt_identity()
        
        # Add token to blacklist
        blacklisted_tokens.add(jti)
        
        # Log logout
        log_user_action(
            user_id=user_id,
            action='logout',
            details=f'User logged out from IP: {request.remote_addr}',
            ip_address=request.remote_addr
        )
        
        return jsonify({'message': 'Successfully logged out'}), 200
        
    except Exception as e:
        current_app.logger.error(f'Logout error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """
    Refresh access token
    """
    try:
        current_user_id = get_jwt_identity()
        
        # Verify user still exists and is active
        user = User.query.get(current_user_id)
        if not user or user.status != 'active':
            return jsonify({'error': 'User not found or inactive'}), 401
        
        # Create new access token
        new_access_token = create_access_token(identity=current_user_id)
        
        return jsonify({
            'access_token': new_access_token
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Token refresh error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get current user information
    """
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Get current user error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """
    Change user password
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        
        if not current_password or not new_password:
            return jsonify({'error': 'Current password and new password are required'}), 400
        
        if len(new_password) < 8:
            return jsonify({'error': 'New password must be at least 8 characters long'}), 400
        
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Verify current password
        if not user.check_password(current_password):
            return jsonify({'error': 'Current password is incorrect'}), 400
        
        # Set new password
        user.set_password(new_password)
        db.session.commit()
        
        # Log password change
        log_user_action(
            user_id=user.id,
            action='password_change',
            details=f'Password changed from IP: {request.remote_addr}',
            ip_address=request.remote_addr
        )
        
        return jsonify({'message': 'Password changed successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f'Change password error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

# JWT token blacklist checker
@auth_bp.before_app_request
def check_if_token_revoked():
    """
    Check if token is blacklisted
    """
    try:
        if request.endpoint and 'auth' in request.endpoint:
            return
        
        # Only check for protected routes
        if hasattr(request, 'jwt_payload'):
            jti = request.jwt_payload.get('jti')
            if jti in blacklisted_tokens:
                return jsonify({'error': 'Token has been revoked'}), 401
    except:
        pass
