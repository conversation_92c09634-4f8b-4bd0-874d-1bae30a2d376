"""
Authentication and authorization utilities
"""

from functools import wraps
from flask import jsonify
from flask_jwt_extended import get_jwt_identity
from backend.models import User

def require_role(allowed_roles):
    """
    Decorator to require specific roles for accessing endpoints
    
    Args:
        allowed_roles: List of allowed roles (e.g., ['admin', 'manager'])
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return jsonify({'error': 'User not found'}), 401
            
            if user.status != 'active':
                return jsonify({'error': 'User account is inactive'}), 401
            
            if user.role not in allowed_roles:
                return jsonify({'error': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_current_user():
    """
    Get the current authenticated user
    """
    current_user_id = get_jwt_identity()
    if current_user_id:
        return User.query.get(current_user_id)
    return None

def require_api_key(f):
    """
    Decorator to require API key authentication for device endpoints
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import request
        from backend.models import Device
        
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            return jsonify({'error': 'API key required'}), 401
        
        device = Device.query.filter_by(api_key=api_key).first()
        if not device:
            return jsonify({'error': 'Invalid API key'}), 401
        
        # Store device in request context for use in the endpoint
        request.current_device = device
        
        return f(*args, **kwargs)
    return decorated_function
