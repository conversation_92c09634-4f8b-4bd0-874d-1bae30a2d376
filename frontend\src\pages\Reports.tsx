import React, { useState } from 'react';
import { Download, Calendar, FileText, DollarSign, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import { apiService } from '@/services/api';
import { ReportParams, AttendanceReportData, LeaveReportData, PayrollReportData } from '@/types/api';
import { formatCurrency, formatHours, getErrorMessage, downloadBlob } from '@/lib/utils';
import { usePermissions } from '@/contexts/AuthContext';

interface ReportFormData {
  startDate: string;
  endDate: string;
  employeeId?: string;
  department?: string;
  format: 'json' | 'csv';
}

const Reports: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('attendance');
  
  const [formData, setFormData] = useState<ReportFormData>({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    format: 'json',
  });

  const permissions = usePermissions();

  if (!permissions.canViewReports()) {
    return (
      <div className="flex items-center justify-center h-64">
        <ErrorMessage message="You don't have permission to view reports." />
      </div>
    );
  }

  const handleInputChange = (field: keyof ReportFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const generateReport = async (reportType: 'attendance' | 'leave' | 'payroll') => {
    try {
      setIsLoading(true);
      setError(null);

      const params: ReportParams = {
        start_date: formData.startDate,
        end_date: formData.endDate,
        format: formData.format,
      };

      if (formData.employeeId && formData.employeeId !== 'all') {
        params.employee_id = parseInt(formData.employeeId);
      }

      if (formData.department && formData.department !== 'all') {
        params.department = formData.department;
      }

      let response;
      switch (reportType) {
        case 'attendance':
          response = await apiService.getAttendanceReport(params);
          break;
        case 'leave':
          response = await apiService.getLeaveReport(params);
          break;
        case 'payroll':
          response = await apiService.getPayrollReport(params);
          break;
      }

      if (formData.format === 'csv' && response instanceof Blob) {
        // Download CSV file
        const filename = `${reportType}-report-${formData.startDate}-to-${formData.endDate}.csv`;
        downloadBlob(response, filename);
      } else {
        // Display JSON data
        setReportData(response);
      }
    } catch (err) {
      setError(getErrorMessage(err));
    } finally {
      setIsLoading(false);
    }
  };

  const ReportForm: React.FC<{ onGenerate: () => void }> = ({ onGenerate }) => (
    <Card>
      <CardHeader>
        <CardTitle>Report Parameters</CardTitle>
        <CardDescription>
          Configure the parameters for your report
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="startDate">Start Date</Label>
            <Input
              id="startDate"
              type="date"
              value={formData.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="endDate">End Date</Label>
            <Input
              id="endDate"
              type="date"
              value={formData.endDate}
              onChange={(e) => handleInputChange('endDate', e.target.value)}
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="employeeId">Employee (Optional)</Label>
            <Select onValueChange={(value) => handleInputChange('employeeId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All Employees" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                {/* Would need to fetch employees list */}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="department">Department (Optional)</Label>
            <Select onValueChange={(value) => handleInputChange('department', value)}>
              <SelectTrigger>
                <SelectValue placeholder="All Departments" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                <SelectItem value="Engineering">Engineering</SelectItem>
                <SelectItem value="Marketing">Marketing</SelectItem>
                <SelectItem value="HR">HR</SelectItem>
                <SelectItem value="Sales">Sales</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="format">Format</Label>
          <Select onValueChange={(value) => handleInputChange('format', value as 'json' | 'csv')} defaultValue="json">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="json">View Online (JSON)</SelectItem>
              <SelectItem value="csv">Download CSV</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button onClick={onGenerate} disabled={isLoading} className="w-full">
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Generating Report...
            </>
          ) : (
            <>
              <Download className="mr-2 h-4 w-4" />
              Generate Report
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );

  const AttendanceReportTable: React.FC<{ data: AttendanceReportData[] }> = ({ data }) => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse border border-gray-300">
        <thead>
          <tr className="bg-gray-50">
            <th className="border border-gray-300 px-4 py-2 text-left">Employee</th>
            <th className="border border-gray-300 px-4 py-2 text-left">Department</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Hours Worked</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Clock Ins</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Clock Outs</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Total Wages</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="border border-gray-300 px-4 py-2">{row.employee_name}</td>
              <td className="border border-gray-300 px-4 py-2">{row.department}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{formatHours(row.hours_worked)}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{row.clock_ins}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{row.clock_outs}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{formatCurrency(row.total_wages)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const LeaveReportTable: React.FC<{ data: LeaveReportData[] }> = ({ data }) => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse border border-gray-300">
        <thead>
          <tr className="bg-gray-50">
            <th className="border border-gray-300 px-4 py-2 text-left">Employee</th>
            <th className="border border-gray-300 px-4 py-2 text-left">Department</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Leave Balance</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Sick Leave Balance</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Total Leave Taken</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Pending Requests</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="border border-gray-300 px-4 py-2">{row.employee_name}</td>
              <td className="border border-gray-300 px-4 py-2">{row.department}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{row.leave_balance} days</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{row.sick_leave_balance} days</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{row.total_leave_taken} days</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{row.pending_requests}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const PayrollReportTable: React.FC<{ data: PayrollReportData[] }> = ({ data }) => (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse border border-gray-300">
        <thead>
          <tr className="bg-gray-50">
            <th className="border border-gray-300 px-4 py-2 text-left">Employee</th>
            <th className="border border-gray-300 px-4 py-2 text-left">Department</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Regular Hours</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Overtime Hours</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Gross Pay</th>
            <th className="border border-gray-300 px-4 py-2 text-right">Net Pay</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="border border-gray-300 px-4 py-2">{row.employee_name}</td>
              <td className="border border-gray-300 px-4 py-2">{row.department}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{formatHours(row.regular_hours)}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{formatHours(row.overtime_hours)}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{formatCurrency(row.gross_pay)}</td>
              <td className="border border-gray-300 px-4 py-2 text-right">{formatCurrency(row.net_pay)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
        <p className="text-muted-foreground">
          Generate and download various reports for analysis
        </p>
      </div>

      {error && <ErrorMessage message={error} />}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="attendance">
            <FileText className="mr-2 h-4 w-4" />
            Attendance
          </TabsTrigger>
          <TabsTrigger value="leave">
            <Calendar className="mr-2 h-4 w-4" />
            Leave
          </TabsTrigger>
          <TabsTrigger value="payroll">
            <DollarSign className="mr-2 h-4 w-4" />
            Payroll
          </TabsTrigger>
        </TabsList>

        <TabsContent value="attendance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <ReportForm onGenerate={() => generateReport('attendance')} />
            <Card>
              <CardHeader>
                <CardTitle>Attendance Report</CardTitle>
                <CardDescription>
                  Track employee attendance, hours worked, and wages
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Employee attendance records</li>
                  <li>• Total hours worked per employee</li>
                  <li>• Clock-in and clock-out counts</li>
                  <li>• Calculated wages based on hourly rates</li>
                  <li>• Auto-checkout incidents</li>
                </ul>
              </CardContent>
            </Card>
          </div>
          {reportData && reportData.data && (
            <Card>
              <CardHeader>
                <CardTitle>Report Results</CardTitle>
              </CardHeader>
              <CardContent>
                <AttendanceReportTable data={reportData.data} />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="leave" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <ReportForm onGenerate={() => generateReport('leave')} />
            <Card>
              <CardHeader>
                <CardTitle>Leave Report</CardTitle>
                <CardDescription>
                  Analyze employee leave usage and balances
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Current leave balances</li>
                  <li>• Leave taken during period</li>
                  <li>• Sick leave vs regular leave usage</li>
                  <li>• Pending leave requests</li>
                  <li>• Leave approval statistics</li>
                </ul>
              </CardContent>
            </Card>
          </div>
          {reportData && reportData.data && (
            <Card>
              <CardHeader>
                <CardTitle>Report Results</CardTitle>
              </CardHeader>
              <CardContent>
                <LeaveReportTable data={reportData.data} />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="payroll" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <ReportForm onGenerate={() => generateReport('payroll')} />
            <Card>
              <CardHeader>
                <CardTitle>Payroll Report</CardTitle>
                <CardDescription>
                  Calculate wages and payroll information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Regular and overtime hours</li>
                  <li>• Gross and net pay calculations</li>
                  <li>• Tax deductions</li>
                  <li>• Hourly rates and total wages</li>
                  <li>• Department-wise payroll summary</li>
                </ul>
              </CardContent>
            </Card>
          </div>
          {reportData && reportData.data && (
            <Card>
              <CardHeader>
                <CardTitle>Report Results</CardTitle>
              </CardHeader>
              <CardContent>
                <PayrollReportTable data={reportData.data} />
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Reports;
