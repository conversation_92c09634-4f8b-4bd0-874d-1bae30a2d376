"""
Settings management routes for the Time and Attendance System
"""

from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from backend.models import Setting, db
from backend.utils.logging import log_user_action
from backend.utils.auth import require_role

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('', methods=['GET'])
@jwt_required()
@require_role(['admin'])
def get_settings():
    """
    Get all settings (admin only)
    """
    try:
        settings = Setting.query.order_by(Setting.setting_key).all()
        settings_data = [setting.to_dict() for setting in settings]
        
        return jsonify({
            'data': settings_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Get settings error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@settings_bp.route('/<string:key>', methods=['GET'])
@jwt_required()
@require_role(['admin'])
def get_setting(key):
    """
    Get specific setting by key (admin only)
    """
    try:
        setting = Setting.query.filter_by(setting_key=key).first()
        
        if not setting:
            return jsonify({'error': 'Setting not found'}), 404
        
        return jsonify(setting.to_dict()), 200
        
    except Exception as e:
        current_app.logger.error(f'Get setting error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@settings_bp.route('/<string:key>', methods=['PUT'])
@jwt_required()
@require_role(['admin'])
def update_setting(key):
    """
    Update setting (admin only)
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        value = data.get('value')
        if value is None:
            return jsonify({'error': 'Value is required'}), 400
        
        # Convert value to string for storage
        value_str = str(value)
        
        setting = Setting.query.filter_by(setting_key=key).first()
        
        if setting:
            # Update existing setting
            old_value = setting.setting_value
            setting.setting_value = value_str
            setting.updated_at = datetime.utcnow()
            
            if 'description' in data:
                setting.description = data['description']
            
            action_details = f'Updated setting {key}: {old_value} -> {value_str}'
        else:
            # Create new setting
            setting = Setting(
                setting_key=key,
                setting_value=value_str,
                description=data.get('description')
            )
            db.session.add(setting)
            action_details = f'Created setting {key}: {value_str}'
        
        db.session.commit()
        
        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='update_setting',
            entity_type='setting',
            entity_id=setting.id,
            details=action_details
        )
        
        return jsonify(setting.to_dict()), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Update setting error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@settings_bp.route('/<string:key>', methods=['DELETE'])
@jwt_required()
@require_role(['admin'])
def delete_setting(key):
    """
    Delete setting (admin only)
    """
    try:
        setting = Setting.query.filter_by(setting_key=key).first()
        
        if not setting:
            return jsonify({'error': 'Setting not found'}), 404
        
        db.session.delete(setting)
        db.session.commit()
        
        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='delete_setting',
            entity_type='setting',
            entity_id=setting.id,
            details=f'Deleted setting: {key}'
        )
        
        return jsonify({'message': 'Setting deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Delete setting error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@settings_bp.route('/bulk', methods=['PUT'])
@jwt_required()
@require_role(['admin'])
def update_settings_bulk():
    """
    Update multiple settings at once (admin only)
    """
    try:
        data = request.get_json()
        if not data or 'settings' not in data:
            return jsonify({'error': 'Settings data required'}), 400
        
        settings_data = data['settings']
        if not isinstance(settings_data, dict):
            return jsonify({'error': 'Settings must be a dictionary'}), 400
        
        updated_settings = []
        
        for key, value in settings_data.items():
            if not key:
                continue
            
            value_str = str(value) if value is not None else ''
            
            setting = Setting.query.filter_by(setting_key=key).first()
            
            if setting:
                setting.setting_value = value_str
                setting.updated_at = datetime.utcnow()
            else:
                setting = Setting(
                    setting_key=key,
                    setting_value=value_str
                )
                db.session.add(setting)
            
            updated_settings.append(setting)
        
        db.session.commit()
        
        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='bulk_update_settings',
            entity_type='setting',
            details=f'Updated {len(updated_settings)} settings'
        )
        
        return jsonify({
            'message': f'Updated {len(updated_settings)} settings',
            'data': [setting.to_dict() for setting in updated_settings]
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Bulk update settings error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@settings_bp.route('/initialize', methods=['POST'])
@jwt_required()
@require_role(['admin'])
def initialize_default_settings():
    """
    Initialize default system settings (admin only)
    """
    try:
        default_settings = [
            {
                'key': 'auto_checkout_time',
                'value': '19:30',
                'description': 'Automatic checkout time (24-hour format)'
            },
            {
                'key': 'work_hours_per_day',
                'value': '8',
                'description': 'Standard work hours per day'
            },
            {
                'key': 'work_days_per_week',
                'value': '5',
                'description': 'Standard work days per week'
            },
            {
                'key': 'annual_leave_days',
                'value': '25',
                'description': 'Annual leave days per year'
            },
            {
                'key': 'sick_leave_days',
                'value': '10',
                'description': 'Sick leave days per year'
            },
            {
                'key': 'overtime_multiplier',
                'value': '1.5',
                'description': 'Overtime pay multiplier'
            },
            {
                'key': 'company_name',
                'value': 'Your Company Name',
                'description': 'Company name for reports'
            },
            {
                'key': 'timezone',
                'value': 'UTC',
                'description': 'System timezone'
            },
            {
                'key': 'currency',
                'value': 'EUR',
                'description': 'Currency for wage calculations'
            },
            {
                'key': 'email_notifications',
                'value': 'true',
                'description': 'Enable email notifications'
            }
        ]
        
        created_count = 0
        
        for setting_data in default_settings:
            existing_setting = Setting.query.filter_by(setting_key=setting_data['key']).first()
            
            if not existing_setting:
                setting = Setting(
                    setting_key=setting_data['key'],
                    setting_value=setting_data['value'],
                    description=setting_data['description']
                )
                db.session.add(setting)
                created_count += 1
        
        db.session.commit()
        
        # Log action
        current_user_id = get_jwt_identity()
        log_user_action(
            user_id=current_user_id,
            action='initialize_settings',
            entity_type='setting',
            details=f'Initialized {created_count} default settings'
        )
        
        return jsonify({
            'message': f'Initialized {created_count} default settings'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Initialize settings error: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500
