# Backend Architecture and Database Schema for Time and Attendance System

## Overview
This document outlines the backend architecture and database schema for the time and attendance system, including RESTful API endpoints, authentication mechanisms, data models, and integration points.

## Technology Stack
- **Framework**: Flask (Python)
- **Database**: MySQL
- **Authentication**: JWT (JSON Web Tokens)
- **API Format**: RESTful JSON
- **Documentation**: Swagger/OpenAPI

## Database Schema

### Tables

#### 1. employees
```sql
CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rfid_tag VARCHAR(50) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    department VARCHAR(50),
    position VARCHAR(50),
    hourly_rate DECIMAL(10,2) NOT NULL,  -- In Euro
    leave_balance DECIMAL(10,2) NOT NULL DEFAULT 0,
    sick_leave_balance DECIMAL(10,2) NOT NULL DEFAULT 0,
    employment_start_date DATE NOT NULL,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. attendance_records
```sql
CREATE TABLE attendance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    timestamp DATETIME NOT NULL,
    type ENUM('clock_in', 'clock_out') NOT NULL,
    device_id VARCHAR(50) NOT NULL,
    status ENUM('regular', 'auto_checkout', 'manual_adjustment') NOT NULL DEFAULT 'regular',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
);
```

#### 3. leave_requests
```sql
CREATE TABLE leave_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    type ENUM('sick_leave', 'regular_leave') NOT NULL,
    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    approver_id INT,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES users(id) ON DELETE SET NULL
);
```

#### 4. devices
```sql
CREATE TABLE devices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    mac_address VARCHAR(17) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    api_key VARCHAR(255) UNIQUE NOT NULL,
    last_sync DATETIME,
    status ENUM('online', 'offline') DEFAULT 'offline',
    firmware_version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 5. users
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('admin', 'manager', 'viewer') NOT NULL DEFAULT 'viewer',
    last_login DATETIME,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 6. system_logs
```sql
CREATE TABLE system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

#### 7. settings
```sql
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## RESTful API Endpoints

### Authentication
- **POST /api/auth/login**
  - Description: Authenticate user and return JWT token
  - Request Body: `{ "username": "string", "password": "string" }`
  - Response: `{ "token": "string", "user": { "id": "integer", "username": "string", "role": "string" } }`

- **POST /api/auth/logout**
  - Description: Invalidate JWT token (add to blacklist)
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "message": "Logged out successfully" }`

- **POST /api/auth/refresh**
  - Description: Refresh JWT token
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "token": "string" }`

### Employees
- **GET /api/employees**
  - Description: Get all employees
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters: 
    - `status` (optional): Filter by status (active/inactive)
    - `department` (optional): Filter by department
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "rfid_tag": "string", "first_name": "string", "last_name": "string", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

- **GET /api/employees/{id}**
  - Description: Get employee by ID
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "id": "integer", "rfid_tag": "string", "first_name": "string", "last_name": "string", ... }`

- **POST /api/employees**
  - Description: Create new employee
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "rfid_tag": "string", "first_name": "string", "last_name": "string", "email": "string", "hourly_rate": "number", ... }`
  - Response: `{ "id": "integer", "rfid_tag": "string", "first_name": "string", ... }`

- **PUT /api/employees/{id}**
  - Description: Update employee
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "first_name": "string", "last_name": "string", ... }`
  - Response: `{ "id": "integer", "rfid_tag": "string", "first_name": "string", ... }`

- **DELETE /api/employees/{id}**
  - Description: Delete employee (or set to inactive)
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "message": "Employee deleted successfully" }`

- **GET /api/employees/{id}/attendance**
  - Description: Get attendance records for an employee
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `start_date` (optional): Filter by start date
    - `end_date` (optional): Filter by end date
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "timestamp": "datetime", "type": "string", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

- **GET /api/employees/{id}/leave**
  - Description: Get leave requests for an employee
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `status` (optional): Filter by status (pending/approved/rejected)
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "start_date": "date", "end_date": "date", "type": "string", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

### Attendance
- **GET /api/attendance**
  - Description: Get all attendance records
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `employee_id` (optional): Filter by employee ID
    - `start_date` (optional): Filter by start date
    - `end_date` (optional): Filter by end date
    - `type` (optional): Filter by type (clock_in/clock_out)
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "employee_id": "integer", "timestamp": "datetime", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

- **POST /api/attendance**
  - Description: Create new attendance record (used by ESP32 devices)
  - Headers: `X-API-Key: {device_api_key}`
  - Request Body: `{ "employee_rfid": "string", "timestamp": "datetime", "type": "string", "device_id": "string", "status": "string", "notes": "string" }`
  - Response: `{ "id": "integer", "employee_id": "integer", "timestamp": "datetime", ... }`

- **POST /api/attendance/batch**
  - Description: Create multiple attendance records (used by ESP32 devices for syncing)
  - Headers: `X-API-Key: {device_api_key}`
  - Request Body: `{ "records": [ { "employee_rfid": "string", "timestamp": "datetime", "type": "string", ... }, ... ] }`
  - Response: `{ "success_count": "integer", "failed_count": "integer", "failed_records": [ { "index": "integer", "reason": "string" }, ... ] }`

- **PUT /api/attendance/{id}**
  - Description: Update attendance record (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "timestamp": "datetime", "type": "string", "status": "string", "notes": "string" }`
  - Response: `{ "id": "integer", "employee_id": "integer", "timestamp": "datetime", ... }`

- **DELETE /api/attendance/{id}**
  - Description: Delete attendance record (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "message": "Attendance record deleted successfully" }`

### Leave Requests
- **GET /api/leave**
  - Description: Get all leave requests
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `employee_id` (optional): Filter by employee ID
    - `status` (optional): Filter by status (pending/approved/rejected)
    - `start_date` (optional): Filter by start date
    - `end_date` (optional): Filter by end date
    - `type` (optional): Filter by type (sick_leave/regular_leave)
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "employee_id": "integer", "start_date": "date", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

- **POST /api/leave**
  - Description: Create new leave request
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "employee_id": "integer", "start_date": "date", "end_date": "date", "type": "string", "notes": "string" }`
  - Response: `{ "id": "integer", "employee_id": "integer", "start_date": "date", ... }`

- **PUT /api/leave/{id}**
  - Description: Update leave request
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "start_date": "date", "end_date": "date", "type": "string", "notes": "string" }`
  - Response: `{ "id": "integer", "employee_id": "integer", "start_date": "date", ... }`

- **PUT /api/leave/{id}/status**
  - Description: Update leave request status (approve/reject)
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "status": "string", "notes": "string" }`
  - Response: `{ "id": "integer", "employee_id": "integer", "status": "string", ... }`

- **DELETE /api/leave/{id}**
  - Description: Delete leave request
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "message": "Leave request deleted successfully" }`

### Devices
- **GET /api/devices**
  - Description: Get all devices
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `status` (optional): Filter by status (online/offline)
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "name": "string", "location": "string", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

- **GET /api/devices/{id}**
  - Description: Get device by ID
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "id": "integer", "name": "string", "location": "string", ... }`

- **POST /api/devices**
  - Description: Register new device
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "name": "string", "location": "string", "mac_address": "string" }`
  - Response: `{ "id": "integer", "name": "string", "location": "string", "api_key": "string", ... }`

- **PUT /api/devices/{id}**
  - Description: Update device
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "name": "string", "location": "string" }`
  - Response: `{ "id": "integer", "name": "string", "location": "string", ... }`

- **DELETE /api/devices/{id}**
  - Description: Delete device
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "message": "Device deleted successfully" }`

- **POST /api/devices/{id}/regenerate-key**
  - Description: Regenerate API key for device
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "id": "integer", "api_key": "string" }`

### Reports
- **GET /api/reports/attendance**
  - Description: Generate attendance report
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `employee_id` (optional): Filter by employee ID
    - `department` (optional): Filter by department
    - `start_date`: Start date for report
    - `end_date`: End date for report
    - `format` (optional): Report format (pdf/csv), defaults to pdf
  - Response: Binary file (PDF or CSV)

- **GET /api/reports/leave**
  - Description: Generate leave report
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `employee_id` (optional): Filter by employee ID
    - `department` (optional): Filter by department
    - `start_date`: Start date for report
    - `end_date`: End date for report
    - `format` (optional): Report format (pdf/csv), defaults to pdf
  - Response: Binary file (PDF or CSV)

- **GET /api/reports/payroll**
  - Description: Generate payroll report
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `employee_id` (optional): Filter by employee ID
    - `department` (optional): Filter by department
    - `start_date`: Start date for report
    - `end_date`: End date for report
    - `format` (optional): Report format (pdf/csv), defaults to pdf
  - Response: Binary file (PDF or CSV)

### Users (Admin)
- **GET /api/users**
  - Description: Get all users (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Query Parameters:
    - `role` (optional): Filter by role
    - `status` (optional): Filter by status
    - `page` (optional): Page number for pagination
    - `limit` (optional): Items per page
  - Response: `{ "data": [ { "id": "integer", "username": "string", "email": "string", ... } ], "total": "integer", "page": "integer", "limit": "integer" }`

- **POST /api/users**
  - Description: Create new user (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "username": "string", "password": "string", "email": "string", "first_name": "string", "last_name": "string", "role": "string" }`
  - Response: `{ "id": "integer", "username": "string", "email": "string", ... }`

- **PUT /api/users/{id}**
  - Description: Update user (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "email": "string", "first_name": "string", "last_name": "string", "role": "string", "status": "string" }`
  - Response: `{ "id": "integer", "username": "string", "email": "string", ... }`

- **PUT /api/users/{id}/password**
  - Description: Update user password (admin or self)
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "password": "string" }`
  - Response: `{ "message": "Password updated successfully" }`

- **DELETE /api/users/{id}**
  - Description: Delete user (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "message": "User deleted successfully" }`

### Settings
- **GET /api/settings**
  - Description: Get all settings (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Response: `{ "data": [ { "key": "string", "value": "string", "description": "string" }, ... ] }`

- **PUT /api/settings/{key}**
  - Description: Update setting (admin only)
  - Headers: `Authorization: Bearer {token}`
  - Request Body: `{ "value": "string" }`
  - Response: `{ "key": "string", "value": "string", "description": "string" }`

## Authentication and Security

### JWT Authentication
- **Token Structure**: Header, Payload (user ID, username, role, expiration), Signature
- **Token Expiration**: 24 hours
- **Token Refresh**: Enabled, with refresh token valid for 7 days
- **Token Storage**: Client-side in secure HTTP-only cookies or localStorage (with appropriate security measures)

### API Key Authentication (for Devices)
- Each device has a unique API key stored in the `devices` table
- API key is required for device-specific endpoints
- API key is sent in the `X-API-Key` header

### Password Security
- Passwords are hashed using bcrypt with appropriate salt rounds
- Password complexity requirements enforced (minimum length, character types)
- Failed login attempt limiting to prevent brute force attacks

### Data Protection
- All sensitive data is encrypted in transit using HTTPS
- Database credentials are stored securely in environment variables
- Input validation and sanitization to prevent SQL injection and XSS attacks
- CORS configuration to restrict API access to authorized domains

## Data Processing Logic

### Hours Calculation
- Calculate hours worked between clock-in and clock-out pairs
- Handle special cases:
  - Auto-checkouts (marked as not counted towards hours worked)
  - Overnight shifts spanning multiple days
  - Breaks and lunch periods (if tracked)

### Leave Balance Management
- Deduct used leave from employee's leave balance when leave is approved
- Add leave allocations based on company policy (e.g., monthly accrual)
- Track separate balances for sick leave and regular leave
- Handle leave balance adjustments (manual additions/deductions)

### Wage Calculation
- Calculate wages based on hours worked and hourly rate
- Handle overtime calculations if applicable
- Account for special rates (weekends, holidays)
- Generate payroll reports with detailed breakdowns

## Email Notifications
- Send notifications for:
  - Leave request submissions
  - Leave request status changes (approved/rejected)
  - System alerts (e.g., device offline for extended period)
  - Report generation completion
- Use a reliable email service (e.g., SMTP server or third-party service like SendGrid)

## Backup and Recovery
- Implement automated database backups (daily)
- Store backups securely (encrypted, off-site)
- Document recovery procedures
- Test backup restoration periodically

## Implementation Considerations

### Database Indexes
```sql
-- Employees table
CREATE INDEX idx_employees_rfid_tag ON employees(rfid_tag);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_employees_department ON employees(department);

-- Attendance records table
CREATE INDEX idx_attendance_employee_id ON attendance_records(employee_id);
CREATE INDEX idx_attendance_timestamp ON attendance_records(timestamp);
CREATE INDEX idx_attendance_type ON attendance_records(type);
CREATE INDEX idx_attendance_status ON attendance_records(status);

-- Leave requests table
CREATE INDEX idx_leave_employee_id ON leave_requests(employee_id);
CREATE INDEX idx_leave_dates ON leave_requests(start_date, end_date);
CREATE INDEX idx_leave_status ON leave_requests(status);
CREATE INDEX idx_leave_type ON leave_requests(type);

-- Devices table
CREATE INDEX idx_devices_mac_address ON devices(mac_address);
CREATE INDEX idx_devices_status ON devices(status);
```

### API Rate Limiting
- Implement rate limiting to prevent abuse
- Different limits for different endpoints based on sensitivity and resource requirements
- Higher limits for authenticated users compared to unauthenticated requests

### Logging and Monitoring
- Log all API requests and responses for debugging and audit purposes
- Monitor system performance and resource usage
- Set up alerts for unusual activity or system issues
- Implement structured logging for easier analysis

### Error Handling
- Consistent error response format across all endpoints
- Appropriate HTTP status codes for different error types
- Detailed error messages for developers (in development environment)
- Generic error messages for users (in production environment)

### API Documentation
- Generate Swagger/OpenAPI documentation
- Include example requests and responses
- Document authentication requirements
- Provide error code explanations
