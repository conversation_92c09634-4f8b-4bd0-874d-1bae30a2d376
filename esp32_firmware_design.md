# ESP32 Firmware Design for Time and Attendance System

## Overview
This document outlines the design for the ESP32 firmware, covering hardware interaction, core logic, connectivity, and data management for the time and attendance system.

## Required Libraries
- **MFRC522.h**: For interfacing with the RFID-RC522 module.
- **SPI.h**: Required for SPI communication (RFID and TFT).
- **TFT_eSPI.h**: For controlling the 1.8" TFT SPI display.
- **WiFi.h**: For WiFi connectivity.
- **HTTPClient.h**: For making HTTP requests to the backend API.
- **NTPClient.h**: For synchronizing time with an NTP server.
- **ArduinoJson.h**: For parsing and generating JSON data for API communication.
- **FS.h** & **SPIFFS.h**: For using the SPI Flash File System (SPIFFS) for local storage.
- **WiFiUdp.h**: Required by NTPClient.
- **TimeLib.h**: For time manipulation.

## Firmware Structure
```cpp
// Include Libraries

// Pin Definitions (RFID, TFT)

// Global Variables (WiFi credentials, Server URL, Device ID, etc.)

// Object Instantiations (MFRC522, TFT_eSPI, WiFiClient, HTTPClient, NTPClient, WiFiUDP)

// Function Prototypes

void setup() {
  // Initialize Serial, SPI, RFID, TFT, WiFi, NTP, SPIFFS
  // Display initial information
}

void loop() {
  // Check for RFID tag scan
  // Update display (time, status)
  // Check for pending syncs
  // Handle automatic checkout logic
  // Maintain WiFi connection
  // Handle potential employee registration mode
}

// Helper Functions
// - connectWiFi()
// - getTime()
// - displayInfo()
// - handleRFIDScan()
// - processClockInOut()
// - storeRecordLocally()
// - syncWithBackend()
// - checkForAutoCheckout()
// - registerNewEmployee()
```

## Core Functionality Implementation

### 1. Hardware Initialization (setup())
- Initialize Serial communication for debugging.
- Initialize SPI bus.
- Initialize MFRC522 RFID reader.
- Initialize TFT_eSPI display, clear screen, set text properties.
- Initialize SPIFFS for local storage.
- Connect to WiFi.
- Initialize NTP client and synchronize time.
- Display initial status (Time, Date, WiFi Status, Device Ready).

### 2. RFID Tag Scanning (handleRFIDScan())
- Check if a new RFID card is present using `mfrc522.PICC_IsNewCardPresent()` and `mfrc522.PICC_ReadCardSerial()`.
- If a card is detected, read its UID.
- Convert UID to a string format.
- Check if this UID corresponds to a known employee (potentially cache employee list locally or query backend).
- Trigger `processClockInOut()` with the employee UID.
- Provide visual feedback on the TFT display (e.g., "Scan Successful" or "Unknown Tag").
- Implement a short delay or debounce logic to prevent multiple reads from a single scan.

### 3. TFT Display Interface (displayInfo())
- Function to update the display periodically and after events.
- **Content**: Current Time, Current Date, WiFi Connection Status, Last Scan Status (Employee Name, In/Out), System Messages.
- Use `tft.fillScreen()`, `tft.setCursor()`, `tft.setTextColor()`, `tft.setTextSize()`, `tft.println()`.
- Optimize updates to avoid flickering (e.g., only redraw changed elements).

### 4. WiFi Connectivity (connectWiFi())
- Use `WiFi.begin(ssid, password)`.
- Loop with a timeout while checking `WiFi.status() != WL_CONNECTED`.
- Handle connection failures and retries.
- Store WiFi credentials securely (consider hardcoding for simplicity initially, or a configuration portal for flexibility).
- Periodically check connection status in `loop()` and attempt reconnection if needed.

### 5. Time Synchronization (getTime())
- Use `NTPClient` library configured with a suitable NTP server (e.g., `pool.ntp.org`) and timezone offset (Europe/User's specific location).
- Call `ntpClient.update()` periodically in `loop()`.
- Use `ntpClient.getFormattedTime()` or `ntpClient.getEpochTime()` for timestamps.
- Ensure time is synchronized before processing clock-ins/outs.

### 6. Time Tracking Logic (processClockInOut())
- **Input**: Employee UID.
- **Lookup**: Determine the employee's current status (In/Out). This might require a small local cache or a quick backend query if feasible.
- **Toggle Status**: If employee is "Out", clock them "In". If "In", clock them "Out".
- **Timestamp**: Get the current synchronized time.
- **Record**: Create an attendance record (Employee UID, Timestamp, Status [In/Out], Source [Device ID]).
- **Store Locally**: Call `storeRecordLocally()` to save the record to SPIFFS.
- **Display**: Update TFT with employee name and new status.
- **Sync**: Flag that there are pending records to sync.

### 7. Automatic Checkout (checkForAutoCheckout())
- Run this check periodically in `loop()`, perhaps every minute.
- Get the current time.
- If the time is 7:30 PM (19:30):
    - Iterate through a list of currently clocked-in employees (requires maintaining this state locally).
    - For each employee still clocked in:
        - Create a clock-out record with the current timestamp (19:30).
        - Add a specific note/flag indicating "Auto-checkout, not punched".
        - Store this record locally using `storeRecordLocally()`.
        - Update the employee's local status to "Out".
        - Flag for synchronization.
- This requires maintaining a reliable list of who is currently 'In'. This list could be stored in RAM or SPIFFS. Synchronization with the backend is crucial to ensure accuracy.

### 8. Local Storage (storeRecordLocally())
- Use SPIFFS to store attendance records.
- Define a file naming convention (e.g., `/attendance_log.csv` or `/pending_sync.json`).
- **Format**: Store records in a simple format like CSV or JSON array.
  - CSV Example: `timestamp,employee_uid,status,synced_flag,note`
  - JSON Example: `[{"ts": 1678886400, "uid": "ABCDEF01", "status": "In", "synced": false, "note": ""}, ...]`
- Append new records to the file.
- Implement functions to read pending records and mark them as synced after successful backend communication.
- Handle potential file system errors (e.g., full storage).

### 9. Backend Synchronization (syncWithBackend())
- Run periodically (e.g., every 5 minutes) or when a certain number of unsynced records accumulate.
- Check WiFi connection status.
- Read unsynced records from SPIFFS.
- Format data into JSON payload for the backend API.
- Create an HTTPS POST request to the backend endpoint (e.g., `/api/attendance`).
- Include a device authentication token in the request header.
- **Security**: Use HTTPS (requires `WiFiClientSecure` and potentially root certificates).
- Handle API response:
    - **Success (200 OK)**: Mark the sent records as synced in the local storage (e.g., update the `synced_flag` or remove them from a pending file).
    - **Failure (e.g., 4xx, 5xx)**: Log the error, keep records as unsynced, potentially implement a retry mechanism with backoff.
- Handle network errors.

### 10. Employee Registration Mode
- Implement a trigger mechanism (e.g., holding a button during boot, or a specific admin command/scan).
- When in registration mode:
    - Display "Scan New Tag for Registration" on TFT.
    - Wait for an RFID scan.
    - Once a tag is scanned, capture its UID.
    - Prompt admin (via Serial or a simple interface) to associate this UID with employee details (this part is tricky without a keyboard/complex UI on the device itself. A more practical approach is to register tags via the web dashboard and sync the employee list down to the ESP32).
    - **Alternative (Web-driven)**: The web dashboard handles adding employees and assigning tags. The ESP32 periodically fetches the updated employee list/tag mappings from the backend.

### 11. Error Handling
- Implement checks for hardware initialization failures (RFID, TFT).
- Handle WiFi connection errors gracefully.
- Manage NTP sync failures.
- Log errors during API communication.
- Handle SPIFFS read/write errors.
- Provide feedback on the TFT display for critical errors.

## Security Considerations
- **WiFi Credentials**: Avoid hardcoding directly in the source if possible. Consider using a configuration portal or storing them in NVS (Non-Volatile Storage).
- **API Communication**: Always use HTTPS to encrypt data in transit.
- **Device Authentication**: Implement a secure method for the ESP32 to authenticate with the backend (e.g., unique device ID and secret key/token).
- **Backend Validation**: The backend must validate all data received from the ESP32.

## Future Enhancements
- Over-the-Air (OTA) updates for firmware.
- Configuration portal for WiFi and server settings.
- Local caching of employee names for faster display after scans.
- More sophisticated error reporting.
- Buzzer for audible feedback.
