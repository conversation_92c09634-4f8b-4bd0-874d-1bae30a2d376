# Time and Attendance System - Complete Setup Guide

This guide will walk you through setting up the complete Time and Attendance System, including the backend server, database, and ESP32 firmware.

## Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS, or Linux
- **Python**: Version 3.8 or higher
- **MySQL/MariaDB**: Version 8.0 or higher
- **Node.js**: Version 16 or higher (for frontend development)
- **A<PERSON><PERSON>o IDE**: Version 1.8.19 or higher (for ESP32 firmware)

### Hardware Requirements
- ESP32 Development Board
- MFRC522 RFID Reader Module
- 1.8" TFT SPI Display (128x160, ST7735)
- RFID Cards/Tags (13.56MHz)
- Breadboard and jumper wires
- Power supply (5V/2A)

## Step 1: Database Setup

### Install MySQL/MariaDB

#### Windows
1. Download MySQL from https://dev.mysql.com/downloads/mysql/
2. Run the installer and follow the setup wizard
3. Set a root password and remember it

#### macOS
```bash
brew install mysql
brew services start mysql
mysql_secure_installation
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

### Create Database
1. Connect to MySQL:
   ```bash
   mysql -u root -p
   ```

2. Create the database:
   ```sql
   CREATE DATABASE time_attendance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'attendance_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON time_attendance.* TO 'attendance_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

3. Import the schema:
   ```bash
   mysql -u attendance_user -p time_attendance < backend/database/schema.sql
   ```

## Step 2: Backend Setup

### Install Python Dependencies
1. Navigate to the project directory:
   ```bash
   cd time_attendance_system
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - **Windows**: `venv\Scripts\activate`
   - **macOS/Linux**: `source venv/bin/activate`

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Configure Environment
1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your settings:
   ```env
   # Database Configuration
   DATABASE_URL=mysql+pymysql://attendance_user:your_password@localhost/time_attendance
   
   # Security Keys (generate new ones!)
   SECRET_KEY=your-secret-key-here
   JWT_SECRET_KEY=your-jwt-secret-key-here
   
   # Email Configuration (optional)
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USE_TLS=true
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-app-password
   ```

### Initialize Database
```bash
python backend/database/init_db.py
```

This will create all tables and insert sample data.

### Start the Backend Server
```bash
python run_backend.py
```

The server will start on `http://localhost:5000`

## Step 3: ESP32 Firmware Setup

### Install Arduino IDE and Libraries
1. Download and install Arduino IDE from https://www.arduino.cc/en/software
2. Install ESP32 board package:
   - Go to File > Preferences
   - Add to Additional Board Manager URLs:
     ```
     https://dl.espressif.com/dl/package_esp32_index.json
     ```
   - Go to Tools > Board > Boards Manager
   - Search "ESP32" and install "ESP32 by Espressif Systems"

3. Install required libraries via Library Manager:
   - MFRC522 by GithubCommunity
   - TFT_eSPI by Bodmer
   - ArduinoJson by Benoit Blanchon
   - NTPClient by Fabrice Weinberg

### Configure TFT_eSPI Library
1. Navigate to Arduino libraries folder (usually `Documents/Arduino/libraries/`)
2. Open `TFT_eSPI/User_Setup.h`
3. Configure for ST7735 display:
   ```cpp
   #define ST7735_DRIVER
   #define TFT_WIDTH  128
   #define TFT_HEIGHT 160
   #define TFT_CS   15
   #define TFT_DC    2
   #define TFT_RST   4
   ```

### Hardware Assembly
Connect components according to this wiring diagram:

#### MFRC522 RFID Reader
| MFRC522 | ESP32 |
|---------|-------|
| VCC     | 3.3V  |
| GND     | GND   |
| RST     | GPIO22|
| SDA     | GPIO21|
| MOSI    | GPIO23|
| MISO    | GPIO19|
| SCK     | GPIO18|

#### TFT Display
| TFT | ESP32 |
|-----|-------|
| VCC | 3.3V  |
| GND | GND   |
| CS  | GPIO15|
| RST | GPIO4 |
| DC  | GPIO2 |
| MOSI| GPIO23|
| SCK | GPIO18|

### Configure and Upload Firmware
1. Open `esp32_firmware/time_attendance_system/time_attendance_system.ino`
2. Edit configuration in the sketch:
   ```cpp
   const char* ssid = "YOUR_WIFI_SSID";
   const char* password = "YOUR_WIFI_PASSWORD";
   const char* serverURL = "http://your-server-ip:5000";
   const char* deviceAPIKey = "your-device-api-key";
   ```

3. Get the device API key from the backend:
   - Login to the system with admin credentials (admin/admin123)
   - Go to Devices section
   - Find your device and copy the API key

4. Select board: Tools > Board > ESP32 Dev Module
5. Select correct port: Tools > Port
6. Upload the sketch

## Step 4: Device Registration

### Register ESP32 Device
1. Access the backend API or web interface
2. Login with admin credentials
3. Register a new device:
   ```bash
   curl -X POST http://localhost:5000/api/devices \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Main Entrance Scanner",
       "location": "Building A - Main Entrance",
       "mac_address": "AA:BB:CC:DD:EE:FF"
     }'
   ```

4. Note the generated API key and update the ESP32 firmware

## Step 5: Testing

### Test Backend API
1. Test authentication:
   ```bash
   curl -X POST http://localhost:5000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}'
   ```

2. Test employee creation:
   ```bash
   curl -X POST http://localhost:5000/api/employees \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "rfid_tag": "TEST1234",
       "first_name": "Test",
       "last_name": "Employee",
       "hourly_rate": 25.00,
       "employment_start_date": "2024-01-01"
     }'
   ```

### Test ESP32 Device
1. Power on the ESP32
2. Check Serial Monitor (115200 baud) for connection status
3. Test RFID scanning with a card
4. Verify attendance records appear in the backend

## Step 6: Production Deployment

### Backend Deployment
1. Use a production WSGI server like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 "backend.app:create_app()"
   ```

2. Set up a reverse proxy with Nginx:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

3. Configure SSL with Let's Encrypt:
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

### Security Considerations
1. Change default admin password
2. Generate secure secret keys
3. Configure firewall rules
4. Set up database backups
5. Enable HTTPS for all communications
6. Regularly update dependencies

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL service is running
   - Verify credentials in .env file
   - Ensure database exists

2. **ESP32 WiFi Connection Failed**
   - Check SSID and password
   - Ensure 2.4GHz network (ESP32 doesn't support 5GHz)
   - Check signal strength

3. **RFID Not Working**
   - Verify wiring connections
   - Check RFID reader power (3.3V)
   - Test with different RFID cards

4. **Server Communication Failed**
   - Check server URL and port
   - Verify API key is correct
   - Check firewall settings

### Getting Help
1. Check log files for error messages
2. Use Serial Monitor for ESP32 debugging
3. Verify all connections and configurations
4. Test each component individually

## Next Steps

After successful setup:
1. Add your employees to the system
2. Distribute RFID cards to employees
3. Configure auto-checkout times
4. Set up email notifications
5. Generate and review attendance reports
6. Plan for system backups and maintenance

## Maintenance

### Regular Tasks
- Daily database backups
- Weekly log file rotation
- Monthly security updates
- Quarterly system health checks

### Monitoring
- Monitor server uptime
- Check ESP32 device connectivity
- Review attendance data for anomalies
- Monitor storage usage

This completes the setup of your Time and Attendance System. The system is now ready for production use!
