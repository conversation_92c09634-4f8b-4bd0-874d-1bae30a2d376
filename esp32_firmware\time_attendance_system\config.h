/*
 * Configuration file for Time and Attendance System ESP32 Firmware
 * 
 * Modify these settings according to your hardware setup and network configuration
 */

#ifndef CONFIG_H
#define CONFIG_H

// WiFi Configuration
// Replace with your WiFi network credentials
#define WIFI_SSID "YOUR_WIFI_SSID"
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD"

// Server Configuration
// Replace with your backend server details
#define SERVER_URL "http://your-server.com:5000"
#define DEVICE_API_KEY "your-device-api-key"
#define DEVICE_ID "ESP32_001"

// Hardware Pin Configuration
// MFRC522 RFID Reader Pins
#define RFID_RST_PIN    22
#define RFID_SS_PIN     21

// TFT Display Pins (configured in TFT_eSPI library)
// These are the default pins for most ESP32 TFT shields
// MOSI = 23, MISO = 19, SCK = 18, CS = 15, DC = 2, RST = 4
// If your display uses different pins, modify the TFT_eSPI User_Setup.h file

// NTP Configuration
#define NTP_SERVER "pool.ntp.org"
#define GMT_OFFSET_SEC 0        // UTC offset in seconds (0 for UTC)
#define DAYLIGHT_OFFSET_SEC 0   // Daylight saving offset in seconds

// Auto Checkout Configuration
#define AUTO_CHECKOUT_HOUR 19   // 24-hour format (19 = 7 PM)
#define AUTO_CHECKOUT_MINUTE 30

// Timing Configuration (in milliseconds)
#define SYNC_INTERVAL 300000           // 5 minutes
#define DISPLAY_UPDATE_INTERVAL 1000   // 1 second
#define AUTO_CHECKOUT_CHECK_INTERVAL 60000  // 1 minute
#define RFID_SCAN_DELAY 2000          // Delay between RFID scans

// Display Configuration
#define DISPLAY_ROTATION 1  // 0=Portrait, 1=Landscape, 2=Portrait(flipped), 3=Landscape(flipped)

// Employee Name Mapping
// Add your employees' RFID tags and names here
// In a production system, this would be fetched from the server
struct EmployeeMapping {
  const char* rfidTag;
  const char* name;
};

const EmployeeMapping EMPLOYEE_MAPPINGS[] = {
  {"ABCD1234", "John Doe"},
  {"EFGH5678", "Jane Smith"},
  {"IJKL9012", "Bob Johnson"},
  {"MNOP3456", "Alice Brown"},
  // Add more employees as needed
};

#define NUM_EMPLOYEES (sizeof(EMPLOYEE_MAPPINGS) / sizeof(EmployeeMapping))

// System Configuration
#define SERIAL_BAUD_RATE 115200
#define WIFI_CONNECT_TIMEOUT 20000  // 20 seconds
#define HTTP_TIMEOUT 10000          // 10 seconds

// File System Configuration
#define PENDING_RECORDS_FILE "/pending_records.txt"
#define CONFIG_FILE "/config.json"
#define LOG_FILE "/system.log"

// Debug Configuration
#define DEBUG_ENABLED true
#define DEBUG_RFID true
#define DEBUG_WIFI true
#define DEBUG_SERVER true
#define DEBUG_DISPLAY true

// Color Definitions for TFT Display
#define COLOR_BACKGROUND TFT_BLACK
#define COLOR_TEXT_PRIMARY TFT_WHITE
#define COLOR_TEXT_SECONDARY TFT_CYAN
#define COLOR_SUCCESS TFT_GREEN
#define COLOR_ERROR TFT_RED
#define COLOR_WARNING TFT_YELLOW
#define COLOR_INFO TFT_BLUE

// Status Messages
#define MSG_WIFI_CONNECTING "Connecting to WiFi..."
#define MSG_WIFI_CONNECTED "WiFi Connected!"
#define MSG_WIFI_FAILED "WiFi Failed!"
#define MSG_SERVER_ONLINE "Server Online"
#define MSG_SERVER_OFFLINE "Server Offline"
#define MSG_RFID_READY "RFID Ready"
#define MSG_RFID_ERROR "RFID Error!"
#define MSG_TIME_SYNCED "Time Synced"
#define MSG_TIME_SYNC_FAILED "Time Sync Failed"
#define MSG_SCAN_CARD "Scan RFID Card"
#define MSG_CLOCKED_IN "CLOCKED IN"
#define MSG_CLOCKED_OUT "CLOCKED OUT"
#define MSG_STORED_LOCALLY "(Stored Locally)"

// System Limits
#define MAX_PENDING_RECORDS 1000
#define MAX_EMPLOYEE_NAME_LENGTH 50
#define MAX_RFID_TAG_LENGTH 20
#define MAX_TIMESTAMP_LENGTH 25

// Preferences Keys
#define PREF_NAMESPACE "attendance"
#define PREF_LAST_TYPE_PREFIX "last_type_"
#define PREF_DEVICE_ID "device_id"
#define PREF_WIFI_SSID "wifi_ssid"
#define PREF_WIFI_PASSWORD "wifi_password"
#define PREF_SERVER_URL "server_url"
#define PREF_API_KEY "api_key"

#endif // CONFIG_H
