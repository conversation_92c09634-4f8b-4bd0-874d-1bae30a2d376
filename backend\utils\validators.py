"""
Validation utilities for the Time and Attendance System
"""

import re
from datetime import datetime, date
from typing import Dict, Any, Optional

def validate_email(email: str) -> bool:
    """Validate email format"""
    if not email:
        return False
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """Validate phone number format"""
    if not phone:
        return True  # Phone is optional
    # Allow various phone formats
    pattern = r'^[\+]?[1-9][\d]{0,15}$'
    return re.match(pattern, phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')) is not None

def validate_rfid_tag(rfid_tag: str) -> bool:
    """Validate RFID tag format"""
    if not rfid_tag:
        return False
    # RFID tags are typically 8-10 characters, alphanumeric
    pattern = r'^[A-Fa-f0-9]{8,10}$'
    return re.match(pattern, rfid_tag) is not None

def validate_mac_address(mac_address: str) -> bool:
    """Validate MAC address format"""
    if not mac_address:
        return False
    pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    return re.match(pattern, mac_address) is not None

def validate_login_data(data: Dict[str, Any]) -> Optional[str]:
    """Validate login request data"""
    username = data.get('username')
    password = data.get('password')
    
    if not username:
        return 'Username is required'
    
    if not password:
        return 'Password is required'
    
    if len(username) < 3:
        return 'Username must be at least 3 characters long'
    
    if len(password) < 6:
        return 'Password must be at least 6 characters long'
    
    return None

def validate_employee_data(data: Dict[str, Any], is_update: bool = False) -> Optional[str]:
    """Validate employee data"""
    required_fields = ['first_name', 'last_name', 'hourly_rate', 'employment_start_date']
    
    if not is_update:
        required_fields.append('rfid_tag')
    
    # Check required fields
    for field in required_fields:
        if field not in data or not data[field]:
            return f'{field.replace("_", " ").title()} is required'
    
    # Validate specific fields
    if 'rfid_tag' in data and not validate_rfid_tag(data['rfid_tag']):
        return 'Invalid RFID tag format'
    
    if 'email' in data and data['email'] and not validate_email(data['email']):
        return 'Invalid email format'
    
    if 'phone' in data and not validate_phone(data['phone']):
        return 'Invalid phone number format'
    
    if 'hourly_rate' in data:
        try:
            rate = float(data['hourly_rate'])
            if rate < 0:
                return 'Hourly rate must be positive'
        except (ValueError, TypeError):
            return 'Invalid hourly rate format'
    
    if 'employment_start_date' in data:
        try:
            start_date = datetime.strptime(data['employment_start_date'], '%Y-%m-%d').date()
            if start_date > date.today():
                return 'Employment start date cannot be in the future'
        except ValueError:
            return 'Invalid employment start date format (YYYY-MM-DD)'
    
    return None

def validate_attendance_data(data: Dict[str, Any]) -> Optional[str]:
    """Validate attendance record data"""
    required_fields = ['employee_rfid', 'timestamp', 'type', 'device_id']
    
    # Check required fields
    for field in required_fields:
        if field not in data or not data[field]:
            return f'{field.replace("_", " ").title()} is required'
    
    # Validate RFID tag
    if not validate_rfid_tag(data['employee_rfid']):
        return 'Invalid RFID tag format'
    
    # Validate type
    if data['type'] not in ['clock_in', 'clock_out']:
        return 'Type must be either clock_in or clock_out'
    
    # Validate timestamp
    try:
        datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
    except ValueError:
        return 'Invalid timestamp format'
    
    # Validate status if provided
    if 'status' in data and data['status'] not in ['regular', 'auto_checkout', 'manual_adjustment']:
        return 'Invalid status value'
    
    return None

def validate_leave_request_data(data: Dict[str, Any]) -> Optional[str]:
    """Validate leave request data"""
    required_fields = ['employee_id', 'start_date', 'end_date', 'type']
    
    # Check required fields
    for field in required_fields:
        if field not in data or data[field] is None:
            return f'{field.replace("_", " ").title()} is required'
    
    # Validate employee_id
    try:
        employee_id = int(data['employee_id'])
        if employee_id <= 0:
            return 'Invalid employee ID'
    except (ValueError, TypeError):
        return 'Invalid employee ID format'
    
    # Validate dates
    try:
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        
        if start_date > end_date:
            return 'Start date must be before or equal to end date'
        
        if start_date < date.today():
            return 'Start date cannot be in the past'
            
    except ValueError:
        return 'Invalid date format (YYYY-MM-DD)'
    
    # Validate type
    if data['type'] not in ['sick_leave', 'regular_leave']:
        return 'Type must be either sick_leave or regular_leave'
    
    return None

def validate_device_data(data: Dict[str, Any]) -> Optional[str]:
    """Validate device data"""
    required_fields = ['name', 'mac_address']
    
    # Check required fields
    for field in required_fields:
        if field not in data or not data[field]:
            return f'{field.replace("_", " ").title()} is required'
    
    # Validate MAC address
    if not validate_mac_address(data['mac_address']):
        return 'Invalid MAC address format'
    
    return None

def validate_user_data(data: Dict[str, Any], is_update: bool = False) -> Optional[str]:
    """Validate user data"""
    required_fields = ['username', 'email', 'first_name', 'last_name']
    
    if not is_update:
        required_fields.append('password')
    
    # Check required fields
    for field in required_fields:
        if field not in data or not data[field]:
            return f'{field.replace("_", " ").title()} is required'
    
    # Validate email
    if not validate_email(data['email']):
        return 'Invalid email format'
    
    # Validate username
    if len(data['username']) < 3:
        return 'Username must be at least 3 characters long'
    
    # Validate password if provided
    if 'password' in data and data['password']:
        if len(data['password']) < 8:
            return 'Password must be at least 8 characters long'
    
    # Validate role if provided
    if 'role' in data and data['role'] not in ['admin', 'manager', 'viewer']:
        return 'Invalid role value'
    
    # Validate status if provided
    if 'status' in data and data['status'] not in ['active', 'inactive']:
        return 'Invalid status value'
    
    return None

def validate_pagination_params(page: str, limit: str) -> tuple:
    """Validate and convert pagination parameters"""
    try:
        page_num = int(page) if page else 1
        limit_num = int(limit) if limit else 20
        
        # Ensure positive values
        page_num = max(1, page_num)
        limit_num = max(1, min(100, limit_num))  # Cap at 100
        
        return page_num, limit_num
    except ValueError:
        return 1, 20
