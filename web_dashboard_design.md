# Web Dashboard UI/UX Design for Time and Attendance System

## Overview
This document outlines the design for the web-based dashboard of the time and attendance system, focusing on user interface, user experience, responsive design, and integration with the backend API.

## Technology Stack
- **Frontend Framework**: React with TypeScript
- **CSS Framework**: Tailwind CSS with shadcn/ui components
- **Icons**: Lucide icons
- **Charts/Graphs**: Recharts
- **State Management**: React Context API or Redux Toolkit
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form with Zod validation
- **Authentication**: JWT stored in HTTP-only cookies

## Application Structure

### Directory Structure
```
src/
├── assets/
│   ├── images/
│   └── styles/
├── components/
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   ├── Input.tsx
│   │   └── ...
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   ├── Footer.tsx
│   │   └── Layout.tsx
│   ├── dashboard/
│   ├── employees/
│   ├── attendance/
│   ├── leave/
│   ├── reports/
│   └── settings/
├── contexts/
│   ├── AuthContext.tsx
│   └── ...
├── hooks/
│   ├── useAuth.ts
│   ├── useApi.ts
│   └── ...
├── pages/
│   ├── Login.tsx
│   ├── Dashboard.tsx
│   ├── Employees.tsx
│   ├── EmployeeDetail.tsx
│   ├── Attendance.tsx
│   ├── Leave.tsx
│   ├── Reports.tsx
│   ├── Settings.tsx
│   └── ...
├── services/
│   ├── api.ts
│   ├── auth.ts
│   ├── employees.ts
│   ├── attendance.ts
│   └── ...
├── types/
│   ├── employee.ts
│   ├── attendance.ts
│   ├── leave.ts
│   └── ...
├── utils/
│   ├── date.ts
│   ├── format.ts
│   └── ...
├── App.tsx
└── main.tsx
```

## Page Designs

### 1. Login Page

**UI Components:**
- Company logo
- Login form with username and password fields
- "Remember me" checkbox
- Login button
- Forgot password link
- Error message display area

**Functionality:**
- Form validation for required fields
- Authentication against backend API
- Store JWT token in HTTP-only cookie
- Redirect to dashboard on successful login
- Display error messages for failed login attempts

**Mockup:**
```
+------------------------------------------+
|                                          |
|              [Company Logo]              |
|                                          |
|  +------------------------------------+  |
|  |           Time & Attendance        |  |
|  +------------------------------------+  |
|                                          |
|  +------------------------------------+  |
|  | Username                           |  |
|  +------------------------------------+  |
|                                          |
|  +------------------------------------+  |
|  | Password                    [👁️]    |  |
|  +------------------------------------+  |
|                                          |
|  [✓] Remember me      [Forgot Password]  |
|                                          |
|  +------------------------------------+  |
|  |               LOGIN                |  |
|  +------------------------------------+  |
|                                          |
+------------------------------------------+
```

### 2. Dashboard Page

**UI Components:**
- Header with company logo, user profile dropdown, notifications
- Sidebar navigation
- Status cards (Total Employees, Present Today, On Leave, Late)
- Real-time attendance chart
- Recent activity feed
- Calendar widget showing today's absences
- Quick actions panel

**Functionality:**
- Real-time updates of employee status
- Interactive charts with filtering options
- Quick navigation to detailed views
- Notifications for important events

**Mockup:**
```
+------------------------------------------+
| [Logo]    Dashboard                [User]|
+--------+-------------------------------+-+
|        |                               | |
|        | Status Cards                  | |
|        | +-----+ +-----+ +-----+ +----+| |
|        | |Total| |Pres.| |Leave| |Late|| |
|        | |  42 | |  35 | |   5 | |  2 || |
|        | +-----+ +-----+ +-----+ +----+| |
|        |                               | |
| Nav    | Attendance Chart              | |
| Menu   | +---------------------------+ | |
|        | |                           | | |
| - Home | |                           | | |
|        | |                           | | |
| - Empl.| +---------------------------+ | |
|        |                               | |
| - Att. | Recent Activity    | Calendar | |
|        | +---------------+  | +-------+| |
| - Leave| | J. Smith in   |  | |       || |
|        | | A. Doe out    |  | |       || |
| - Rep. | | M. Brown leave|  | |       || |
|        | +---------------+  | +-------+| |
| - Set. |                               | |
|        | Quick Actions                 | |
+--------+-------------------------------+-+
```

### 3. Employees Page

**UI Components:**
- Search and filter bar
- Add employee button
- Employee data table with columns:
  - Employee ID
  - Name
  - Department
  - Position
  - Status (Present/Absent/On Leave)
  - Actions (View, Edit, Delete)
- Pagination controls
- Export options (CSV, PDF)

**Functionality:**
- Search by name, ID, department
- Filter by status, department
- Sort by any column
- Add new employee with modal form
- Edit employee details
- View detailed employee profile
- Export employee list

**Mockup:**
```
+------------------------------------------+
| [Logo]    Employees               [User] |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | Search: [____________] [Filter]| |
| Menu   |                               | |
|        | [+ Add Employee]    [Export ▼] | |
|        |                               | |
|        | +---------------------------+ | |
|        | | ID | Name | Dept | Status | | |
|        | +---------------------------+ | |
|        | | 001| J.Smith| IT  |Present| | |
|        | | 002| A.Doe  | HR  |Absent | | |
|        | | 003| M.Brown| Fin |Leave  | | |
|        | | ... | ...   | ... | ...   | | |
|        | +---------------------------+ | |
|        |                               | |
|        | [◀] 1 2 3 ... 10 [▶]         | |
|        |                               | |
+--------+-------------------------------+-+
```

### 4. Employee Detail Page

**UI Components:**
- Employee profile header with photo, name, position
- Tabs for different sections:
  - Personal Info
  - Attendance History
  - Leave History
  - Wage Information
- Action buttons (Edit, Delete, Print)
- RFID tag assignment section

**Functionality:**
- View and edit employee details
- View attendance history with filtering options
- View leave history and balance
- Assign/reassign RFID tag
- Calculate wages based on attendance records
- Print employee profile

**Mockup:**
```
+------------------------------------------+
| [Logo]    Employee Detail         [User] |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | [Photo] John Smith            | |
| Menu   |         Software Developer    | |
|        |         ID: EMP001            | |
|        |                               | |
|        | [Edit] [Delete] [Print]       | |
|        |                               | |
|        | [Info] [Attendance] [Leave] [Wage]|
|        | +---------------------------+ | |
|        | |                           | | |
|        | | Content based on selected | | |
|        | | tab                       | | |
|        | |                           | | |
|        | +---------------------------+ | |
|        |                               | |
|        | RFID Tag: AB12CD34           | |
|        | [Assign New Tag]              | |
|        |                               | |
+--------+-------------------------------+-+
```

### 5. Attendance Page

**UI Components:**
- Date range selector
- Department/Employee filter
- Real-time status view
- Attendance data table with columns:
  - Employee ID
  - Name
  - Clock In
  - Clock Out
  - Hours Worked
  - Status (Regular/Auto-checkout/Manual)
  - Notes
- Manual entry button
- Export options

**Functionality:**
- Filter attendance by date range, department, employee
- View real-time attendance status
- Add manual attendance entries
- Edit existing entries (admin only)
- Export attendance data
- View detailed attendance statistics

**Mockup:**
```
+------------------------------------------+
| [Logo]    Attendance             [User]  |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | Date: [From ▼] to [To ▼]      | |
| Menu   |                               | |
|        | Filter: [All Departments ▼]   | |
|        |         [All Employees ▼]     | |
|        |                               | |
|        | [+ Manual Entry]    [Export ▼] | |
|        |                               | |
|        | +---------------------------+ | |
|        | | ID | Name | In    | Out   | | |
|        | +---------------------------+ | |
|        | | 001| J.Smith| 08:02 | 17:05| | |
|        | | 002| A.Doe  | 08:15 | --:--| | |
|        | | 003| M.Brown| --:-- | --:--| | |
|        | | ... | ...   | ...   | ...  | | |
|        | +---------------------------+ | |
|        |                               | |
|        | [◀] 1 2 3 ... 10 [▶]         | |
|        |                               | |
+--------+-------------------------------+-+
```

### 6. Calendar View

**UI Components:**
- Month/Week/Day view toggle
- Month navigation (Previous/Next)
- Calendar grid with:
  - Date cells
  - Employee attendance status indicators
  - Leave indicators
  - Special events
- Legend for status indicators
- Quick view panel for selected date

**Functionality:**
- Toggle between month, week, and day views
- Navigate between months/weeks/days
- View attendance and leave status for all employees
- Click on date to view detailed information
- Add/edit events or notes

**Mockup:**
```
+------------------------------------------+
| [Logo]    Calendar               [User]  |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | [Month] [Week] [Day]  May 2025| |
| Menu   |                               | |
|        | [◀ Prev]            [Next ▶]  | |
|        |                               | |
|        | +---------------------------+ | |
|        | | Mon Tue Wed Thu Fri Sat Sun| | |
|        | +---------------------------+ | |
|        | |  1   2   3   4   5   6   7 | | |
|        | |  8   9   10  11  12  13  14| | |
|        | |  15  16  17  18  19  20  21| | |
|        | |  22  23  24  25  26  27  28| | |
|        | |  29  30  31                | | |
|        | +---------------------------+ | |
|        |                               | |
|        | Selected: May 15, 2025        | |
|        | Present: 38  Absent: 4  Leave: 3|
|        |                               | |
+--------+-------------------------------+-+
```

### 7. Leave Management Page

**UI Components:**
- Leave request form
- Calendar view of leave schedule
- Leave requests table with columns:
  - Employee
  - Type (Sick/Regular)
  - Start Date
  - End Date
  - Status (Pending/Approved/Rejected)
  - Actions
- Leave balance display
- Approval workflow buttons

**Functionality:**
- Submit new leave requests
- View pending, approved, and rejected requests
- Approve or reject requests (for managers/admins)
- View leave calendar to check overlapping requests
- Track leave balance for each employee
- Email notifications for request status changes

**Mockup:**
```
+------------------------------------------+
| [Logo]    Leave Management        [User] |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | [+ New Request]     [Calendar]| |
| Menu   |                               | |
|        | Leave Requests:               | |
|        | [All ▼] [Pending ▼] [Export ▼]| |
|        |                               | |
|        | +---------------------------+ | |
|        | | Emp | Type | Dates  | Status| | |
|        | +---------------------------+ | |
|        | | J.S | Sick | 05/10-05/12|Pend| | |
|        | | A.D | Reg  | 05/15-05/20|Appr| | |
|        | | M.B | Sick | 05/08-05/09|Rejc| | |
|        | | ... | ...  | ...   | ...  | | |
|        | +---------------------------+ | |
|        |                               | |
|        | Leave Balance:                | |
|        | Regular: 15 days  Sick: 5 days| |
|        |                               | |
+--------+-------------------------------+-+
```

### 8. Wage Calculator

**UI Components:**
- Employee selector
- Date range selector
- Hourly rate display/editor
- Hours worked summary
- Deductions input
- Total calculation
- Export/Print buttons

**Functionality:**
- Select employee and date range
- Automatically calculate hours worked from attendance records
- Apply hourly rate from employee profile
- Add/edit deductions
- Calculate total wages
- Generate printable wage slip
- Export to PDF/CSV

**Mockup:**
```
+------------------------------------------+
| [Logo]    Wage Calculator         [User] |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | Employee: [Select Employee ▼] | |
| Menu   |                               | |
|        | Period: [From ▼] to [To ▼]    | |
|        |                               | |
|        | Hourly Rate: €15.50 [Edit]    | |
|        |                               | |
|        | +---------------------------+ | |
|        | | Date    | Hours | Amount  | | |
|        | +---------------------------+ | |
|        | | 05/01/25| 8.0   | €124.00 | | |
|        | | 05/02/25| 8.5   | €131.75 | | |
|        | | ...     | ...   | ...     | | |
|        | +---------------------------+ | |
|        | Total Hours: 168.5           | |
|        | Gross Amount: €2,611.75      | |
|        | Deductions: €0.00 [Add]      | |
|        | Net Amount: €2,611.75        | |
|        |                               | |
|        | [Generate Slip] [Export ▼]    | |
|        |                               | |
+--------+-------------------------------+-+
```

### 9. Reports Page

**UI Components:**
- Report type selector
- Parameters form (date range, employees, departments)
- Generate button
- Preview area
- Export options (PDF, CSV)
- Scheduled reports section

**Functionality:**
- Select from predefined report types
- Set report parameters
- Generate and preview reports
- Export reports in different formats
- Schedule recurring reports
- Access previously generated reports

**Mockup:**
```
+------------------------------------------+
| [Logo]    Reports                 [User] |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | Report Type: [Attendance ▼]   | |
| Menu   |                               | |
|        | Date Range: [From ▼] to [To ▼]| |
|        |                               | |
|        | Employees: [All ▼]            | |
|        | Departments: [All ▼]          | |
|        |                               | |
|        | [Generate Report]             | |
|        |                               | |
|        | Preview:                      | |
|        | +---------------------------+ | |
|        | |                           | | |
|        | | Report preview will appear| | |
|        | | here after generation     | | |
|        | |                           | | |
|        | +---------------------------+ | |
|        |                               | |
|        | [Export PDF] [Export CSV]     | |
|        |                               | |
+--------+-------------------------------+-+
```

### 10. Settings Page

**UI Components:**
- Tabs for different settings categories:
  - General
  - Users
  - Devices
  - Notifications
  - Backup
- Settings forms with save buttons
- User management table
- Device management table

**Functionality:**
- Configure system settings
- Manage user accounts and permissions
- Register and manage ESP32 devices
- Configure notification settings
- Set up automated backups
- View system logs

**Mockup:**
```
+------------------------------------------+
| [Logo]    Settings               [User]  |
+--------+-------------------------------+-+
|        |                               | |
| Nav    | [General] [Users] [Devices] [Notif]|
| Menu   |                               | |
|        | +---------------------------+ | |
|        | |                           | | |
|        | | Settings content based on | | |
|        | | selected tab              | | |
|        | |                           | | |
|        | | [Save Changes]            | | |
|        | |                           | | |
|        | +---------------------------+ | |
|        |                               | |
|        | System Information:           | |
|        | Version: 1.0.0                | |
|        | Last Backup: 2025-05-27 08:00 | |
|        | Database Size: 24.5 MB        | |
|        |                               | |
|        | [Backup Now] [Restore]        | |
|        |                               | |
+--------+-------------------------------+-+
```

## Responsive Design

### Mobile Adaptations
- Collapsible sidebar navigation
- Stacked layout for dashboard cards and widgets
- Simplified tables with horizontal scrolling or card-based views
- Touch-friendly controls with larger tap targets
- Optimized forms with full-width inputs

### Tablet Adaptations
- Sidebar visible on larger tablets, collapsible on smaller ones
- Responsive grid layouts for dashboard widgets
- Optimized table views with prioritized columns
- Adapted calendar view with simplified day cells

### Desktop Adaptations
- Full sidebar navigation always visible
- Multi-column layouts for efficient space usage
- Detailed table views with all columns
- Advanced filtering and sorting options
- Keyboard shortcuts for power users

## Accessibility Considerations
- Semantic HTML structure
- ARIA attributes for interactive elements
- Keyboard navigation support
- Color contrast compliance (WCAG AA)
- Screen reader compatibility
- Focus management for modals and dialogs
- Responsive font sizes

## State Management
- Authentication state (logged in user, permissions)
- UI state (sidebar collapsed/expanded, active filters)
- Data cache (employees, attendance records)
- Form state (validation, submission status)
- Notifications (system messages, alerts)

## API Integration
- RESTful API calls using Axios
- JWT authentication with automatic token refresh
- Request/response interceptors for error handling
- Data caching for improved performance
- Optimistic UI updates for better user experience
- Real-time updates using polling or WebSockets

## Security Measures
- HTTPS for all communications
- JWT stored in HTTP-only cookies
- CSRF protection
- Input validation and sanitization
- Role-based access control
- Session timeout and automatic logout
- Audit logging of user actions

## Implementation Plan
1. Set up React project with TypeScript and Tailwind CSS
2. Implement authentication and user management
3. Create layout components (header, sidebar, footer)
4. Develop dashboard and overview components
5. Implement employee management features
6. Create attendance tracking and visualization
7. Develop leave management system
8. Build wage calculator functionality
9. Implement reporting system
10. Add settings and configuration options
11. Optimize for responsive design and accessibility
12. Integrate with backend API
13. Perform testing and bug fixes
14. Deploy to production
