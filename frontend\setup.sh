#!/bin/bash

# Time and Attendance System Frontend Setup Script
# This script sets up the React TypeScript frontend

set -e

echo "🚀 Setting up Time and Attendance System Frontend..."
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js version 16+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm and try again."
    exit 1
fi

echo "✅ npm version: $(npm -v)"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo ""
    echo "📝 Creating .env file..."
    cat > .env << EOL
# Time and Attendance System Frontend Configuration

# API Configuration
VITE_API_BASE_URL=http://localhost:5000

# Application Configuration
VITE_APP_NAME=Time & Attendance System
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_DEV_MODE=true
EOL
    echo "✅ .env file created"
else
    echo "✅ .env file already exists"
fi

# Run type checking
echo ""
echo "🔍 Running type checking..."
npm run type-check

if [ $? -eq 0 ]; then
    echo "✅ Type checking passed"
else
    echo "❌ Type checking failed"
    exit 1
fi

# Run linting
echo ""
echo "🔍 Running linting..."
npm run lint

if [ $? -eq 0 ]; then
    echo "✅ Linting passed"
else
    echo "⚠️  Linting issues found (non-critical)"
fi

echo ""
echo "🎉 Frontend setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Make sure the backend server is running on http://localhost:5000"
echo "2. Start the development server: npm run dev"
echo "3. Open your browser to http://localhost:3000"
echo "4. Login with default credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "🔧 Available commands:"
echo "  npm run dev      - Start development server"
echo "  npm run build    - Build for production"
echo "  npm run preview  - Preview production build"
echo "  npm run lint     - Run ESLint"
echo "  npm run type-check - Run TypeScript type checking"
echo ""
echo "📚 Documentation:"
echo "  - Frontend README: ./README.md"
echo "  - Project Setup Guide: ../SETUP_GUIDE.md"
echo ""
echo "Happy coding! 🚀"
