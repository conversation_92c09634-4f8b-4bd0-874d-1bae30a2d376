import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  ApiResponse, 
  PaginatedResponse,
  LoginRequest, 
  LoginResponse,
  User,
  Employee,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  AttendanceRecord,
  LeaveRequest,
  CreateLeaveRequest,
  UpdateLeaveStatusRequest,
  Device,
  CreateDeviceRequest,
  UpdateDeviceRequest,
  Setting,
  UpdateSettingRequest,
  ReportParams,
  AttendanceReportData,
  LeaveReportData,
  PayrollReportData,
  ReportResponse,
  DashboardStats,
  EmployeeFilters,
  AttendanceFilters,
  LeaveFilters,
  DeviceFilters,
  UserFilters
} from '@/types/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Try to refresh token
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              const response = await this.refreshToken();
              localStorage.setItem('access_token', response.access_token);
              // Retry the original request
              error.config.headers.Authorization = `Bearer ${response.access_token}`;
              return this.api.request(error.config);
            } catch (refreshError) {
              // Refresh failed, redirect to login
              this.logout();
              window.location.href = '/login';
            }
          } else {
            // No refresh token, redirect to login
            this.logout();
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response: AxiosResponse<LoginResponse> = await this.api.post('/auth/login', credentials);
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      // Ignore errors on logout
    } finally {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
    }
  }

  async refreshToken(): Promise<{ access_token: string }> {
    const refreshToken = localStorage.getItem('refresh_token');
    const response: AxiosResponse<{ access_token: string }> = await this.api.post('/auth/refresh', {}, {
      headers: {
        Authorization: `Bearer ${refreshToken}`,
      },
    });
    return response.data;
  }

  async getCurrentUser(): Promise<{ user: User }> {
    const response: AxiosResponse<{ user: User }> = await this.api.get('/auth/me');
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
    return response.data;
  }

  // Employees
  async getEmployees(filters?: EmployeeFilters): Promise<PaginatedResponse<Employee>> {
    const response: AxiosResponse<PaginatedResponse<Employee>> = await this.api.get('/employees', {
      params: filters,
    });
    return response.data;
  }

  async getEmployee(id: number): Promise<Employee> {
    const response: AxiosResponse<Employee> = await this.api.get(`/employees/${id}`);
    return response.data;
  }

  async createEmployee(employee: CreateEmployeeRequest): Promise<Employee> {
    const response: AxiosResponse<Employee> = await this.api.post('/employees', employee);
    return response.data;
  }

  async updateEmployee(id: number, employee: UpdateEmployeeRequest): Promise<Employee> {
    const response: AxiosResponse<Employee> = await this.api.put(`/employees/${id}`, employee);
    return response.data;
  }

  async deleteEmployee(id: number): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/employees/${id}`);
    return response.data;
  }

  async getEmployeeAttendance(id: number, filters?: AttendanceFilters): Promise<PaginatedResponse<AttendanceRecord>> {
    const response: AxiosResponse<PaginatedResponse<AttendanceRecord>> = await this.api.get(`/employees/${id}/attendance`, {
      params: filters,
    });
    return response.data;
  }

  async getEmployeeLeave(id: number, filters?: LeaveFilters): Promise<PaginatedResponse<LeaveRequest>> {
    const response: AxiosResponse<PaginatedResponse<LeaveRequest>> = await this.api.get(`/employees/${id}/leave`, {
      params: filters,
    });
    return response.data;
  }

  // Attendance
  async getAttendance(filters?: AttendanceFilters): Promise<PaginatedResponse<AttendanceRecord>> {
    const response: AxiosResponse<PaginatedResponse<AttendanceRecord>> = await this.api.get('/attendance', {
      params: filters,
    });
    return response.data;
  }

  async updateAttendance(id: number, data: Partial<AttendanceRecord>): Promise<AttendanceRecord> {
    const response: AxiosResponse<AttendanceRecord> = await this.api.put(`/attendance/${id}`, data);
    return response.data;
  }

  async deleteAttendance(id: number): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/attendance/${id}`);
    return response.data;
  }

  // Leave Requests
  async getLeaveRequests(filters?: LeaveFilters): Promise<PaginatedResponse<LeaveRequest>> {
    const response: AxiosResponse<PaginatedResponse<LeaveRequest>> = await this.api.get('/leave', {
      params: filters,
    });
    return response.data;
  }

  async createLeaveRequest(request: CreateLeaveRequest): Promise<LeaveRequest> {
    const response: AxiosResponse<LeaveRequest> = await this.api.post('/leave', request);
    return response.data;
  }

  async updateLeaveRequest(id: number, request: Partial<CreateLeaveRequest>): Promise<LeaveRequest> {
    const response: AxiosResponse<LeaveRequest> = await this.api.put(`/leave/${id}`, request);
    return response.data;
  }

  async updateLeaveStatus(id: number, status: UpdateLeaveStatusRequest): Promise<LeaveRequest> {
    const response: AxiosResponse<LeaveRequest> = await this.api.put(`/leave/${id}/status`, status);
    return response.data;
  }

  async deleteLeaveRequest(id: number): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/leave/${id}`);
    return response.data;
  }

  // Devices
  async getDevices(filters?: DeviceFilters): Promise<PaginatedResponse<Device>> {
    const response: AxiosResponse<PaginatedResponse<Device>> = await this.api.get('/devices', {
      params: filters,
    });
    return response.data;
  }

  async getDevice(id: number): Promise<Device> {
    const response: AxiosResponse<Device> = await this.api.get(`/devices/${id}`);
    return response.data;
  }

  async createDevice(device: CreateDeviceRequest): Promise<Device> {
    const response: AxiosResponse<Device> = await this.api.post('/devices', device);
    return response.data;
  }

  async updateDevice(id: number, device: UpdateDeviceRequest): Promise<Device> {
    const response: AxiosResponse<Device> = await this.api.put(`/devices/${id}`, device);
    return response.data;
  }

  async deleteDevice(id: number): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/devices/${id}`);
    return response.data;
  }

  async regenerateDeviceKey(id: number): Promise<{ id: number; api_key: string }> {
    const response: AxiosResponse<{ id: number; api_key: string }> = await this.api.post(`/devices/${id}/regenerate-key`);
    return response.data;
  }

  // Users
  async getUsers(filters?: UserFilters): Promise<PaginatedResponse<User>> {
    const response: AxiosResponse<PaginatedResponse<User>> = await this.api.get('/users', {
      params: filters,
    });
    return response.data;
  }

  async createUser(user: Omit<User, 'id' | 'created_at' | 'updated_at' | 'last_login'> & { password: string }): Promise<User> {
    const response: AxiosResponse<User> = await this.api.post('/users', user);
    return response.data;
  }

  async updateUser(id: number, user: Partial<User>): Promise<User> {
    const response: AxiosResponse<User> = await this.api.put(`/users/${id}`, user);
    return response.data;
  }

  async updateUserPassword(id: number, password: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.put(`/users/${id}/password`, { password });
    return response.data;
  }

  async deleteUser(id: number): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/users/${id}`);
    return response.data;
  }

  // Settings
  async getSettings(): Promise<{ data: Setting[] }> {
    const response: AxiosResponse<{ data: Setting[] }> = await this.api.get('/settings');
    return response.data;
  }

  async getSetting(key: string): Promise<Setting> {
    const response: AxiosResponse<Setting> = await this.api.get(`/settings/${key}`);
    return response.data;
  }

  async updateSetting(key: string, setting: UpdateSettingRequest): Promise<Setting> {
    const response: AxiosResponse<Setting> = await this.api.put(`/settings/${key}`, setting);
    return response.data;
  }

  async deleteSetting(key: string): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/settings/${key}`);
    return response.data;
  }

  async initializeSettings(): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.post('/settings/initialize');
    return response.data;
  }

  // Reports
  async getAttendanceReport(params: ReportParams): Promise<ReportResponse<AttendanceReportData> | Blob> {
    const response = await this.api.get('/reports/attendance', {
      params,
      responseType: params.format === 'csv' ? 'blob' : 'json',
    });
    return response.data;
  }

  async getLeaveReport(params: ReportParams): Promise<ReportResponse<LeaveReportData> | Blob> {
    const response = await this.api.get('/reports/leave', {
      params,
      responseType: params.format === 'csv' ? 'blob' : 'json',
    });
    return response.data;
  }

  async getPayrollReport(params: ReportParams): Promise<ReportResponse<PayrollReportData> | Blob> {
    const response = await this.api.get('/reports/payroll', {
      params,
      responseType: params.format === 'csv' ? 'blob' : 'json',
    });
    return response.data;
  }

  // Dashboard
  async getDashboardStats(): Promise<DashboardStats> {
    // This would be a custom endpoint for dashboard statistics
    // For now, we'll aggregate from existing endpoints
    const [employees, attendance, leave, devices] = await Promise.all([
      this.getEmployees({ limit: 1 }),
      this.getAttendance({ limit: 1 }),
      this.getLeaveRequests({ status: 'pending', limit: 1 }),
      this.getDevices({ limit: 1 }),
    ]);

    return {
      total_employees: employees.total,
      active_employees: employees.total, // Would need filtering
      present_today: 0, // Would need today's attendance calculation
      absent_today: 0, // Would need today's attendance calculation
      pending_leave_requests: leave.total,
      online_devices: 0, // Would need filtering
      total_devices: devices.total,
    };
  }

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    const response: AxiosResponse<{ status: string; service: string }> = await this.api.get('/health');
    return response.data;
  }
}

export const apiService = new ApiService();
