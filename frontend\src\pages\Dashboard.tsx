import React, { useEffect, useState } from 'react';
import { Users, UserCheck, UserX, Calendar, Monitor, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorMessage from '@/components/common/ErrorMessage';
import { apiService } from '@/services/api';
import { DashboardStats, Employee, AttendanceRecord, LeaveRequest, Device } from '@/types/api';
import { formatDate, getErrorMessage } from '@/lib/utils';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, description, icon: Icon, trend }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {trend && (
          <p className={`text-xs ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {trend.isPositive ? '+' : ''}{trend.value}% from last month
          </p>
        )}
      </CardContent>
    </Card>
  );
};

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentAttendance, setRecentAttendance] = useState<AttendanceRecord[]>([]);
  const [pendingLeave, setPendingLeave] = useState<LeaveRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch dashboard statistics
        const [employeesResponse, attendanceResponse, leaveResponse, devicesResponse] = await Promise.all([
          apiService.getEmployees({ limit: 1 }),
          apiService.getAttendance({ limit: 10 }),
          apiService.getLeaveRequests({ status: 'pending', limit: 5 }),
          apiService.getDevices({ limit: 1 }),
        ]);

        // Calculate stats
        const dashboardStats: DashboardStats = {
          total_employees: employeesResponse.total,
          active_employees: employeesResponse.total, // Would need filtering in real implementation
          present_today: 0, // Would need today's attendance calculation
          absent_today: 0, // Would need today's attendance calculation
          pending_leave_requests: leaveResponse.total,
          online_devices: 0, // Would need filtering
          total_devices: devicesResponse.total,
        };

        setStats(dashboardStats);
        setRecentAttendance(attendanceResponse.data);
        setPendingLeave(leaveResponse.data);
      } catch (err) {
        setError(getErrorMessage(err));
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return <ErrorMessage message={error} className="m-6" />;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back! Here's what's happening with your team today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Employees"
          value={stats?.total_employees || 0}
          description="Active employees in system"
          icon={Users}
        />
        <StatsCard
          title="Present Today"
          value={stats?.present_today || 0}
          description="Employees currently checked in"
          icon={UserCheck}
        />
        <StatsCard
          title="Absent Today"
          value={stats?.absent_today || 0}
          description="Employees not checked in"
          icon={UserX}
        />
        <StatsCard
          title="Pending Leave"
          value={stats?.pending_leave_requests || 0}
          description="Leave requests awaiting approval"
          icon={Calendar}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Attendance */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Attendance</CardTitle>
            <CardDescription>Latest check-ins and check-outs</CardDescription>
          </CardHeader>
          <CardContent>
            {recentAttendance.length === 0 ? (
              <p className="text-sm text-muted-foreground">No recent attendance records</p>
            ) : (
              <div className="space-y-3">
                {recentAttendance.slice(0, 5).map((record) => (
                  <div key={record.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{record.employee_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(record.timestamp, 'PPp')}
                      </p>
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      record.type === 'clock_in' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {record.type === 'clock_in' ? 'Check In' : 'Check Out'}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Leave Requests */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Leave Requests</CardTitle>
            <CardDescription>Requests awaiting your approval</CardDescription>
          </CardHeader>
          <CardContent>
            {pendingLeave.length === 0 ? (
              <p className="text-sm text-muted-foreground">No pending leave requests</p>
            ) : (
              <div className="space-y-3">
                {pendingLeave.map((request) => (
                  <div key={request.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">{request.employee_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDate(request.start_date)} - {formatDate(request.end_date)}
                      </p>
                    </div>
                    <div className="text-xs px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">
                      {request.type === 'sick_leave' ? 'Sick Leave' : 'Regular Leave'}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
          <CardDescription>Overview of system health and devices</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center space-x-2">
              <Monitor className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Total Devices: {stats?.total_devices || 0}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span className="text-sm">Online: {stats?.online_devices || 0}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 rounded-full bg-red-500"></div>
              <span className="text-sm">Offline: {(stats?.total_devices || 0) - (stats?.online_devices || 0)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
