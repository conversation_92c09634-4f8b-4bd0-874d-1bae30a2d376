# ESP32 Time and Attendance System Firmware

This firmware enables an ESP32 microcontroller to function as a time and attendance tracking device using RFID cards and a TFT display.

## Hardware Requirements

### Components
- ESP32 Development Board (ESP32-WROOM-32 or similar)
- MFRC522 RFID Reader Module
- 1.8" TFT SPI Display (128x160 resolution, ST7735 controller)
- RFID Cards/Tags (13.56MHz)
- Breadboard and jumper wires
- Power supply (5V/2A recommended)

### Wiring Diagram

#### MFRC522 RFID Reader
| MFRC522 Pin | ESP32 Pin |
|-------------|-----------|
| VCC         | 3.3V      |
| GND         | GND       |
| RST         | GPIO 22   |
| SDA (SS)    | GPIO 21   |
| MOSI        | GPIO 23   |
| MISO        | GPIO 19   |
| SCK         | GPIO 18   |

#### 1.8" TFT Display (ST7735)
| TFT Pin | ESP32 Pin |
|---------|-----------|
| VCC     | 3.3V      |
| GND     | GND       |
| CS      | GPIO 15   |
| RST     | GPIO 4    |
| DC      | GPIO 2    |
| MOSI    | GPIO 23   |
| SCK     | GPIO 18   |
| LED     | 3.3V      |

## Software Requirements

### Arduino IDE Setup
1. Install Arduino IDE (version 1.8.19 or later)
2. Install ESP32 board package:
   - Go to File > Preferences
   - Add this URL to Additional Board Manager URLs:
     ```
     https://dl.espressif.com/dl/package_esp32_index.json
     ```
   - Go to Tools > Board > Boards Manager
   - Search for "ESP32" and install "ESP32 by Espressif Systems"

### Required Libraries
Install these libraries through Arduino IDE Library Manager:

1. **MFRC522** by GithubCommunity (version 1.4.10 or later)
2. **TFT_eSPI** by Bodmer (version 2.4.70 or later)
3. **ArduinoJson** by Benoit Blanchon (version 6.21.0 or later)
4. **NTPClient** by Fabrice Weinberg (version 3.2.1 or later)

### TFT_eSPI Configuration
The TFT_eSPI library requires configuration for your specific display:

1. Navigate to your Arduino libraries folder
2. Open `TFT_eSPI/User_Setup.h`
3. Comment out the default driver and uncomment the ST7735 driver:
   ```cpp
   #define ST7735_DRIVER
   ```
4. Set the correct pins:
   ```cpp
   #define TFT_CS   15
   #define TFT_DC    2
   #define TFT_RST   4
   ```
5. Set the display size:
   ```cpp
   #define TFT_WIDTH  128
   #define TFT_HEIGHT 160
   ```

## Configuration

### 1. WiFi and Server Settings
Edit the `config.h` file or modify these constants in the main sketch:

```cpp
// WiFi Configuration
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// Server Configuration
const char* serverURL = "http://your-server.com:5000";
const char* deviceAPIKey = "your-device-api-key";
const char* deviceID = "ESP32_001";
```

### 2. Employee RFID Mapping
Add your employees' RFID tags in the `getEmployeeName()` function:

```cpp
String getEmployeeName(String rfidTag) {
  if (rfidTag == "ABCD1234") return "John Doe";
  if (rfidTag == "EFGH5678") return "Jane Smith";
  // Add more employees...
  return "Unknown Employee";
}
```

### 3. Time Zone Configuration
Adjust the NTP settings for your timezone:

```cpp
const long gmtOffset_sec = 0;  // UTC offset in seconds
const int daylightOffset_sec = 0;  // Daylight saving offset
```

## Installation

1. **Hardware Assembly**
   - Connect all components according to the wiring diagram
   - Ensure all connections are secure
   - Power the ESP32 with a stable 5V supply

2. **Software Upload**
   - Open `time_attendance_system.ino` in Arduino IDE
   - Configure the settings in `config.h`
   - Select the correct board: "ESP32 Dev Module"
   - Select the correct port
   - Upload the sketch

3. **Initial Setup**
   - Open Serial Monitor (115200 baud)
   - The device will attempt to connect to WiFi
   - Once connected, it will sync time with NTP server
   - The display will show the main interface

## Usage

### Normal Operation
1. The display shows current date, time, and system status
2. Employees scan their RFID cards
3. The system automatically toggles between clock-in and clock-out
4. Attendance data is sent to the server immediately (if online)
5. If offline, data is stored locally and synced when connection is restored

### Status Indicators
- **WiFi**: Connected/Disconnected
- **Server**: Online/Offline
- **Last Scan**: Shows the last employee who scanned

### Auto Checkout
- Employees who don't clock out are automatically checked out at 7:30 PM
- Auto-checkout records are marked with special status

## Troubleshooting

### Common Issues

1. **WiFi Connection Failed**
   - Check SSID and password
   - Ensure WiFi network is 2.4GHz (ESP32 doesn't support 5GHz)
   - Check signal strength

2. **RFID Not Working**
   - Verify wiring connections
   - Check if RFID reader is detected (version should appear in Serial Monitor)
   - Try different RFID cards

3. **Display Issues**
   - Verify TFT_eSPI configuration
   - Check display wiring
   - Ensure correct display driver is selected

4. **Server Communication Failed**
   - Check server URL and API key
   - Verify server is running and accessible
   - Check firewall settings

### Debug Information
Enable debug output by setting `DEBUG_ENABLED` to `true` in `config.h`. This will provide detailed information about:
- WiFi connection status
- RFID scan events
- Server communication
- Local storage operations

## Features

### Implemented
- ✅ RFID card scanning
- ✅ TFT display interface
- ✅ WiFi connectivity
- ✅ NTP time synchronization
- ✅ Local data storage (SPIFFS)
- ✅ Server synchronization
- ✅ Auto clock-in/clock-out detection
- ✅ Offline operation support
- ✅ Auto checkout functionality

### Future Enhancements
- 🔄 Employee registration via RFID
- 🔄 Configuration via web interface
- 🔄 Multiple device support
- 🔄 Biometric authentication
- 🔄 Sound feedback
- 🔄 LED status indicators

## API Integration

The firmware communicates with the backend server using these endpoints:

- `POST /api/attendance` - Submit single attendance record
- `POST /api/attendance/batch` - Submit multiple attendance records

Authentication is done using the device API key in the `X-API-Key` header.

## File Structure

```
esp32_firmware/
├── time_attendance_system/
│   ├── time_attendance_system.ino  # Main firmware file
│   └── config.h                    # Configuration file
└── README.md                       # This file
```

## Support

For technical support or questions:
1. Check the troubleshooting section above
2. Review the Serial Monitor output for error messages
3. Ensure all hardware connections are correct
4. Verify server connectivity and API key

## License

This firmware is part of the Time and Attendance System project and is provided as-is for educational and commercial use.
