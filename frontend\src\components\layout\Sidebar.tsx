import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  Clock,
  Calendar,
  FileText,
  Monitor,
  Settings,
  UserCog,
  LogOut,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth, usePermissions } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const location = useLocation();
  const { logout } = useAuth();
  const permissions = usePermissions();

  const navigationItems = [
    {
      title: 'Dashboard',
      href: '/',
      icon: LayoutDashboard,
      show: true,
    },
    {
      title: 'Employees',
      href: '/employees',
      icon: Users,
      show: permissions.canManageEmployees(),
    },
    {
      title: 'Attendance',
      href: '/attendance',
      icon: Clock,
      show: true,
    },
    {
      title: 'Leave Requests',
      href: '/leave',
      icon: Calendar,
      show: true,
    },
    {
      title: 'Reports',
      href: '/reports',
      icon: FileText,
      show: permissions.canViewReports(),
    },
    {
      title: 'Devices',
      href: '/devices',
      icon: Monitor,
      show: permissions.canManageDevices(),
    },
    {
      title: 'Users',
      href: '/users',
      icon: UserCog,
      show: permissions.canManageUsers(),
    },
    {
      title: 'Settings',
      href: '/settings',
      icon: Settings,
      show: permissions.canManageSettings(),
    },
  ];

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className={cn('flex h-full w-64 flex-col bg-card border-r', className)}>
      {/* Logo */}
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center space-x-2">
          <Clock className="h-6 w-6 text-primary" />
          <span className="text-lg font-semibold">TimeTracker</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navigationItems
          .filter(item => item.show)
          .map((item) => {
            const isActive = location.pathname === item.href;
            return (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  'flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                )}
              >
                <item.icon className="h-4 w-4" />
                <span>{item.title}</span>
              </Link>
            );
          })}
      </nav>

      {/* User info and logout */}
      <div className="border-t p-4">
        <div className="mb-3 text-sm text-muted-foreground">
          <div className="font-medium">{permissions.user?.first_name} {permissions.user?.last_name}</div>
          <div className="text-xs">{permissions.user?.role}</div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start"
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
