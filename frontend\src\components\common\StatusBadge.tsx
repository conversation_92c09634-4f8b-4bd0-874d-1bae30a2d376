import React from 'react';
import { Badge } from '@/components/ui/badge';
import { getStatusColor } from '@/lib/utils';

interface StatusBadgeProps {
  status: string;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const getVariant = (status: string) => {
    switch (status) {
      case 'active':
      case 'approved':
      case 'online':
      case 'clock_in':
        return 'success';
      case 'inactive':
      case 'rejected':
      case 'offline':
      case 'clock_out':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const formatStatus = (status: string) => {
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <Badge 
      variant={getVariant(status)} 
      className={className}
    >
      {formatStatus(status)}
    </Badge>
  );
};

export default StatusBadge;
