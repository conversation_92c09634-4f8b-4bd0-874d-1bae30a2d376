-- Time and Attendance System Database Schema
-- MySQL/MariaDB compatible

-- Create database (run this separately if needed)
-- CREATE DATABASE time_attendance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE time_attendance;

-- Users table for admin/manager authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    role ENUM('admin', 'manager', 'viewer') NOT NULL DEFAULT 'viewer',
    last_login DATETIME,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Employees table
CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rfid_tag VARCHAR(50) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    department VARCHAR(50),
    position VARCHAR(50),
    hourly_rate DECIMAL(10,2) NOT NULL,  -- In Euro
    leave_balance DECIMAL(10,2) NOT NULL DEFAULT 0,
    sick_leave_balance DECIMAL(10,2) NOT NULL DEFAULT 0,
    employment_start_date DATE NOT NULL,
    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Attendance records table
CREATE TABLE attendance_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    timestamp DATETIME NOT NULL,
    type ENUM('clock_in', 'clock_out') NOT NULL,
    device_id VARCHAR(50) NOT NULL,
    status ENUM('regular', 'auto_checkout', 'manual_adjustment') NOT NULL DEFAULT 'regular',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
);

-- Leave requests table
CREATE TABLE leave_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    type ENUM('sick_leave', 'regular_leave') NOT NULL,
    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    approver_id INT,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Devices table for ESP32 devices
CREATE TABLE devices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    mac_address VARCHAR(17) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    api_key VARCHAR(255) UNIQUE NOT NULL,
    last_sync DATETIME,
    status ENUM('online', 'offline') DEFAULT 'offline',
    firmware_version VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- System logs table for audit trail
CREATE TABLE system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Settings table for system configuration
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create indexes for better performance
-- Employees table indexes
CREATE INDEX idx_employees_rfid_tag ON employees(rfid_tag);
CREATE INDEX idx_employees_status ON employees(status);
CREATE INDEX idx_employees_department ON employees(department);
CREATE INDEX idx_employees_email ON employees(email);

-- Attendance records table indexes
CREATE INDEX idx_attendance_employee_id ON attendance_records(employee_id);
CREATE INDEX idx_attendance_timestamp ON attendance_records(timestamp);
CREATE INDEX idx_attendance_type ON attendance_records(type);
CREATE INDEX idx_attendance_status ON attendance_records(status);
CREATE INDEX idx_attendance_device_id ON attendance_records(device_id);
CREATE INDEX idx_attendance_employee_timestamp ON attendance_records(employee_id, timestamp);

-- Leave requests table indexes
CREATE INDEX idx_leave_employee_id ON leave_requests(employee_id);
CREATE INDEX idx_leave_dates ON leave_requests(start_date, end_date);
CREATE INDEX idx_leave_status ON leave_requests(status);
CREATE INDEX idx_leave_type ON leave_requests(type);
CREATE INDEX idx_leave_approver_id ON leave_requests(approver_id);

-- Devices table indexes
CREATE INDEX idx_devices_mac_address ON devices(mac_address);
CREATE INDEX idx_devices_api_key ON devices(api_key);
CREATE INDEX idx_devices_status ON devices(status);

-- Users table indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);

-- System logs table indexes
CREATE INDEX idx_logs_user_id ON system_logs(user_id);
CREATE INDEX idx_logs_timestamp ON system_logs(timestamp);
CREATE INDEX idx_logs_action ON system_logs(action);
CREATE INDEX idx_logs_entity ON system_logs(entity_type, entity_id);

-- Settings table indexes
CREATE INDEX idx_settings_key ON settings(setting_key);

-- Insert default admin user (password: admin123)
-- Note: This is a bcrypt hash of 'admin123' - change this in production!
INSERT INTO users (username, password_hash, email, first_name, last_name, role, status) VALUES 
('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlG.', '<EMAIL>', 'System', 'Administrator', 'admin', 'active');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, description) VALUES 
('auto_checkout_time', '19:30', 'Automatic checkout time (24-hour format)'),
('work_hours_per_day', '8', 'Standard work hours per day'),
('work_days_per_week', '5', 'Standard work days per week'),
('annual_leave_days', '25', 'Annual leave days per year'),
('sick_leave_days', '10', 'Sick leave days per year'),
('overtime_multiplier', '1.5', 'Overtime pay multiplier'),
('company_name', 'Your Company Name', 'Company name for reports'),
('timezone', 'UTC', 'System timezone'),
('currency', 'EUR', 'Currency for wage calculations'),
('email_notifications', 'true', 'Enable email notifications');

-- Insert sample employees (optional - for testing)
INSERT INTO employees (rfid_tag, first_name, last_name, email, phone, department, position, hourly_rate, leave_balance, sick_leave_balance, employment_start_date) VALUES 
('ABCD1234', 'John', 'Doe', '<EMAIL>', '+1234567890', 'Engineering', 'Software Developer', 25.00, 25.0, 10.0, '2023-01-15'),
('EFGH5678', 'Jane', 'Smith', '<EMAIL>', '+1234567891', 'Marketing', 'Marketing Manager', 30.00, 25.0, 10.0, '2023-02-01'),
('IJKL9012', 'Bob', 'Johnson', '<EMAIL>', '+1234567892', 'Engineering', 'Senior Developer', 35.00, 25.0, 10.0, '2022-06-01'),
('MNOP3456', 'Alice', 'Brown', '<EMAIL>', '+1234567893', 'HR', 'HR Specialist', 22.00, 25.0, 10.0, '2023-03-15');

-- Insert sample device (optional - for testing)
INSERT INTO devices (name, location, mac_address, ip_address, api_key, firmware_version, status) VALUES 
('Main Entrance Scanner', 'Building A - Main Entrance', 'AA:BB:CC:DD:EE:FF', '*************', 'sample_api_key_change_this_in_production', '1.0.0', 'offline');
