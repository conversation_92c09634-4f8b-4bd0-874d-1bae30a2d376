# Hardware Connection Diagram for ESP32 Time and Attendance System

## Components
1. ESP32 Development Board
2. RFID-RC522 Module
3. 1.8" TFT SPI Display (128x160 resolution, v1.1)
4. Power Supply (5V)
5. Jumper Wires

## Pin Connections

### ESP32 to RFID-RC522 Module (SPI Connection)
| ESP32 Pin | RFID-RC522 Pin | Description |
|-----------|----------------|-------------|
| 3.3V      | 3.3V           | Power       |
| GND       | GND            | Ground      |
| GPIO 5    | SCK            | SPI Clock   |
| GPIO 23   | MOSI           | Master Out Slave In |
| GPIO 19   | MISO           | Master In Slave Out |
| GPIO 22   | SDA/NSS        | Chip Select |
| GPIO 21   | RST            | Reset       |

### ESP32 to 1.8" TFT SPI Display
| ESP32 Pin | TFT Display Pin | Description |
|-----------|-----------------|-------------|
| 3.3V      | VCC             | Power       |
| GND       | GND             | Ground      |
| GPIO 18   | SCK             | SPI Clock   |
| GPIO 2    | SDA/MOSI        | Data Input  |
| GPIO 4    | DC              | Data/Command|
| GPIO 15   | CS              | Chip Select |
| GPIO 0    | RST             | Reset       |
| GPIO 17   | BLK             | Backlight   |

### Power Supply
- Connect 5V power supply to ESP32 Vin pin
- Connect power supply ground to ESP32 GND pin

## Wiring Diagram (ASCII Representation)

```
+---------------+          +---------------+
|               |          |               |
|    ESP32      |          |  RFID-RC522   |
|               |          |               |
|  3.3V o-------+----------o 3.3V          |
|  GND  o-------+----------o GND           |
|  GPIO5 o-------+----------o SCK           |
|  GPIO23 o------+----------o MOSI          |
|  GPIO19 o------+----------o MISO          |
|  GPIO22 o------+----------o SDA/NSS       |
|  GPIO21 o------+----------o RST           |
|               |          |               |
+---------------+          +---------------+

+---------------+          +---------------+
|               |          |               |
|    ESP32      |          |  TFT Display  |
|               |          |               |
|  3.3V o-------+----------o VCC           |
|  GND  o-------+----------o GND           |
|  GPIO18 o------+----------o SCK           |
|  GPIO2 o-------+----------o SDA/MOSI      |
|  GPIO4 o-------+----------o DC            |
|  GPIO15 o------+----------o CS            |
|  GPIO0 o-------+----------o RST           |
|  GPIO17 o------+----------o BLK           |
|               |          |               |
+---------------+          +---------------+

+---------------+          +---------------+
|               |          |               |
|    ESP32      |          | Power Supply  |
|               |          |               |
|  Vin  o-------+----------o 5V            |
|  GND  o-------+----------o GND           |
|               |          |               |
+---------------+          +---------------+
```

## Hardware Considerations

1. **Power Supply**
   - Ensure stable 5V power supply with at least 500mA current capacity
   - Consider adding a capacitor (100μF) between Vin and GND for power stability
   - For portable applications, consider a LiPo battery with charging circuit

2. **RFID-RC522 Module**
   - Mount the RFID reader in an accessible location for easy badge scanning
   - Keep the antenna area clear of metal objects and interference sources
   - Consider adding an LED indicator for successful scans

3. **TFT Display**
   - Mount the display at an appropriate viewing angle
   - Consider adding an anti-glare cover for better visibility
   - Ensure the display is protected from physical damage

4. **ESP32 Protection**
   - House the ESP32 and connections in a suitable enclosure
   - Ensure proper ventilation to prevent overheating
   - Consider adding ESD protection for exposed pins

5. **Wiring**
   - Use appropriate gauge wires for power connections
   - Keep signal wires short to minimize interference
   - Consider using shielded cables for longer connections
   - Use heat shrink tubing or electrical tape to insulate connections

6. **Mounting**
   - Mount the system securely to prevent movement or damage
   - Consider using standoffs for circuit boards
   - Ensure easy access for maintenance and updates

## Testing the Hardware Connections

1. **Power Test**
   - Connect power supply and verify LED indicators on ESP32
   - Measure voltage at key points to ensure proper power distribution

2. **RFID Module Test**
   - Run a simple test program to verify SPI communication
   - Test RFID tag reading functionality
   - Verify reading distance and reliability

3. **Display Test**
   - Run a display initialization test
   - Verify all pixels are functioning
   - Test text and graphics rendering

4. **Full System Test**
   - Verify all components work together without interference
   - Test system under various environmental conditions
   - Verify system stability over extended periods
