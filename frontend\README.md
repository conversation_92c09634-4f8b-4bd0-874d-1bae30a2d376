# Time and Attendance System - Frontend

A modern React TypeScript frontend for the Time and Attendance System, built with Vite, Tailwind CSS, and shadcn/ui components.

## 🚀 Features

### Authentication & Authorization
- JWT-based authentication with automatic token refresh
- Role-based access control (Admin, Manager, Viewer)
- Protected routes and permission-based UI rendering
- Secure logout with token cleanup

### Dashboard
- Real-time system overview with key metrics
- Employee attendance statistics
- Pending leave requests summary
- Device status monitoring
- Recent activity feed

### Employee Management
- Complete CRUD operations for employee profiles
- RFID tag assignment and management
- Department and position tracking
- Leave balance management
- Advanced filtering and search capabilities
- Pagination for large datasets

### Attendance Tracking
- Real-time attendance record viewing
- Clock-in/clock-out status tracking
- Device-based attendance logging
- Filtering by employee, date range, and status
- Export functionality for reports

### Leave Management
- Leave request submission and tracking
- Approval workflow for managers
- Leave balance calculations
- Different leave types (sick, regular)
- Calendar integration for date selection

### Reports & Analytics
- Attendance reports with hours worked and wages
- Leave usage and balance reports
- Payroll calculations with overtime
- Export to CSV format
- Customizable date ranges and filters

### Device Management
- ESP32 device registration and monitoring
- API key management for secure communication
- Device status tracking (online/offline)
- Firmware version monitoring

### User Management
- User account creation and management
- Role assignment and permissions
- Password management
- Account status control

### Settings
- System configuration management
- Company information settings
- Work schedule configuration
- Leave policy settings

## 🛠️ Technology Stack

- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality, accessible UI components
- **React Router** - Client-side routing
- **React Hook Form** - Form handling with validation
- **Zod** - Schema validation
- **Axios** - HTTP client with interceptors
- **date-fns** - Date manipulation and formatting
- **Recharts** - Data visualization (for future analytics)
- **Lucide React** - Beautiful icons

## 📁 Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # shadcn/ui base components
│   │   ├── common/        # Common components (LoadingSpinner, etc.)
│   │   └── layout/        # Layout components (Sidebar, Header)
│   ├── contexts/          # React contexts (AuthContext)
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility functions and configurations
│   ├── pages/             # Page components
│   ├── services/          # API service layer
│   ├── types/             # TypeScript type definitions
│   ├── App.tsx            # Main application component
│   ├── main.tsx           # Application entry point
│   └── index.css          # Global styles and Tailwind imports
├── package.json           # Dependencies and scripts
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── vite.config.ts         # Vite configuration
└── README.md              # This file
```

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ and npm/yarn
- Backend API running on http://localhost:5000

### Installation

1. **Clone the repository and navigate to frontend:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser:**
   Navigate to http://localhost:3000

### Default Login Credentials
- **Username:** admin
- **Password:** admin123

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the frontend directory:

```env
VITE_API_BASE_URL=http://localhost:5000
VITE_APP_NAME=Time & Attendance System
```

### API Configuration
The API base URL is configured in `src/services/api.ts`. By default, it uses `/api` which is proxied to the backend server through Vite's proxy configuration.

### Proxy Configuration
Vite is configured to proxy API requests to the backend:

```typescript
// vite.config.ts
server: {
  port: 3000,
  proxy: {
    '/api': {
      target: 'http://localhost:5000',
      changeOrigin: true,
      secure: false,
    },
  },
},
```

## 🎨 UI Components

The application uses shadcn/ui components for consistent design:

- **Button** - Various button styles and sizes
- **Input** - Form input fields with validation
- **Card** - Content containers
- **Table** - Data tables with sorting and pagination
- **Dialog** - Modal dialogs
- **Select** - Dropdown selections
- **Badge** - Status indicators
- **Tabs** - Tabbed interfaces

### Custom Components
- **LoadingSpinner** - Loading indicators
- **StatusBadge** - Status display with color coding
- **Pagination** - Table pagination controls
- **ErrorMessage** - Error display component

## 🔐 Authentication Flow

1. **Login:** User submits credentials
2. **Token Storage:** JWT tokens stored in localStorage
3. **API Requests:** Access token automatically added to requests
4. **Token Refresh:** Automatic refresh on token expiration
5. **Logout:** Tokens cleared and user redirected

### Protected Routes
Routes are protected using the `ProtectedRoute` component which checks authentication status and redirects to login if needed.

### Role-Based Access
The `usePermissions` hook provides role-based access control:

```typescript
const permissions = usePermissions();

if (permissions.canManageEmployees()) {
  // Show employee management features
}
```

## 📊 State Management

- **Authentication State:** Managed by AuthContext
- **API State:** Managed by individual components with useState
- **Form State:** Managed by React Hook Form
- **Local Storage:** Used for token persistence

## 🔄 API Integration

### Service Layer
The `apiService` class handles all API communication:

```typescript
// Example usage
const employees = await apiService.getEmployees({
  page: 1,
  limit: 20,
  status: 'active'
});
```

### Error Handling
- Automatic token refresh on 401 errors
- Global error handling with user-friendly messages
- Loading states for better UX

### Request Interceptors
- Automatic token attachment
- Request/response logging in development
- Error response handling

## 🎯 Features by Role

### Admin
- Full access to all features
- User management
- Device management
- System settings
- All reports

### Manager
- Employee management
- Leave approval
- Attendance monitoring
- Reports (attendance, leave, payroll)

### Viewer
- Read-only access to attendance
- View own leave requests
- Basic dashboard access

## 🚀 Build and Deployment

### Development Build
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Type Checking
```bash
npm run type-check
```

### Linting
```bash
npm run lint
```

### Preview Production Build
```bash
npm run preview
```

## 🔧 Customization

### Theming
Tailwind CSS configuration can be customized in `tailwind.config.js`. The application uses CSS custom properties for theming.

### Adding New Pages
1. Create page component in `src/pages/`
2. Add route in `src/App.tsx`
3. Add navigation item in `src/components/layout/Sidebar.tsx`
4. Implement permission checks if needed

### Adding New API Endpoints
1. Add types in `src/types/api.ts`
2. Add service methods in `src/services/api.ts`
3. Use in components with proper error handling

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check if backend server is running
   - Verify proxy configuration in vite.config.ts
   - Check network connectivity

2. **Authentication Issues**
   - Clear localStorage and try again
   - Check token expiration
   - Verify backend authentication endpoints

3. **Build Errors**
   - Run `npm run type-check` to identify TypeScript errors
   - Check for missing dependencies
   - Verify import paths

### Development Tips
- Use React Developer Tools for debugging
- Check browser console for errors
- Use network tab to monitor API requests
- Enable TypeScript strict mode for better type safety

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Use TypeScript for all new code
3. Add proper error handling and loading states
4. Include proper accessibility attributes
5. Test on different screen sizes
6. Follow the existing naming conventions

## 📝 License

This project is part of the Time and Attendance System and follows the same license terms.
